using System;
using System.Linq;
using com.luxza.grandartslogic.domain.game.baseball;
using com.luxza.grandartslogic.domain.game.beyondtop;
using com.luxza.grandartslogic.domain.game.countup;
using com.luxza.grandartslogic.domain.game.crcountup;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.cutthroat;
using com.luxza.grandartslogic.domain.game.deltashoot;
using com.luxza.grandartslogic.domain.game.Freeze_zeroone;
using com.luxza.grandartslogic.domain.game.funmission;
using com.luxza.grandartslogic.domain.game.halfit;
using com.luxza.grandartslogic.domain.game.hideandseek;
using com.luxza.grandartslogic.domain.game.hyperbull;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.Oniren;
using com.luxza.grandartslogic.domain.game.pirates;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.shootforce;
using com.luxza.grandartslogic.domain.game.spider;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.targetbull;
using com.luxza.grandartslogic.domain.game.targethat;
using com.luxza.grandartslogic.domain.game.targethorse;
using com.luxza.grandartslogic.domain.game.teamcr;
using com.luxza.grandartslogic.domain.game.tictactoe;
using com.luxza.grandartslogic.domain.game.treasurehunt;
using com.luxza.grandartslogic.domain.game.twolinebattle;
using com.luxza.grandartslogic.domain.game.WildCardcricket;
using com.luxza.grandartslogic.domain.game.zeroone;

namespace com.luxza.grandartslogic.domain.game
{
    /// <summary>
    /// Model with game code defined.
    /// </summary>
    public enum GameCode
    {
        _301,
        _501,
        _701,
        _901,
        _1101,
        _1501,
        _Freeze301,
        _Freeze501,
        _Freeze701,
        _Freeze901,
        _Freeze1101,
        _Freeze1501,
        _KickDown301,
        _KickDown501,
        _StandardCR,
        _HiddenCR,
        _CutThroat,
        _HiddenCutThroat,
        _TeamCR,
        _WildCardCR,
        _Countup,
        _CrCountUp,
        _HalfIt,
        _DeltaShoot,
        _TargetHat,
        _TargetChoice,
        _Oniren,
        _TargetBull,
        _Target20,
        _ShootForce,
        _TargetHorse,
        _MultipleCr,
        _Pirates,
        _Spider,
        _Rotation,
        _BeyondTop,
        _HyperBull,
        _TreasureHunt,
        _2Line,
        _HideAndSeek,
        _FunMission,
        _TicTacToe,
        _ANIMAL_301GAME,
        _ANIMAL_501GAME,
        _ANIMAL_701GAME,
        _ANIMAL_901GAME,
        _ANIMAL_1101GAME,
        _ANIMAL_1501GAME,
        _ANIMAL_STANDARD_CR,

        _BaseBall
    }

    public static class GameCodeExtension
    {
        public static bool IsMedleySupportGame(this GameCode code)
        {
            return code is GameCode._301
                or GameCode._501
                or GameCode._701
                or GameCode._901
                or GameCode._1101
                or GameCode._1501
                or GameCode._StandardCR;
        }

        public static bool IsMedleySupportGame(this GameCode? code)
        {
            if (code == null) return true;
            return code.Value.IsMedleySupportGame();
        }

        public static bool Is01(this GameCode? code)
        {
            return code != null && code.Value.Is01();
        }

        public static bool Is01(this GameCode code)
        {
            return code is GameCode._301
                or GameCode._501
                or GameCode._701
                or GameCode._901
                or GameCode._1101
                or GameCode._1501
                or GameCode._ANIMAL_301GAME
                or GameCode._ANIMAL_501GAME
                or GameCode._ANIMAL_701GAME
                or GameCode._ANIMAL_901GAME
                or GameCode._ANIMAL_1101GAME
                or GameCode._ANIMAL_1501GAME;
        }

        public static bool Is01WithoutAnimal
        (
            this GameCode code
        )
        {
            return code is GameCode._301
                or GameCode._501
                or GameCode._701
                or GameCode._901
                or GameCode._1101
                or GameCode._1501;
        }

        public static bool Is01WithoutAnimal
        (
            this GameCode? code
        )
        {
            return code.Value.Is01WithoutAnimal();
        }

        public static bool IsAnimal01
        (
            this GameCode? code
        )
        {
            return code is GameCode._ANIMAL_301GAME
                or GameCode._ANIMAL_501GAME
                or GameCode._ANIMAL_701GAME
                or GameCode._ANIMAL_901GAME
                or GameCode._ANIMAL_1101GAME
                or GameCode._ANIMAL_1501GAME;
        }

        public static bool IsFreeze01(this GameCode? code)
        {
            if (code == null) return false;
            return code.Value.IsFreeze01();
        }

        public static bool IsFreeze01(this GameCode code)
        {
            return code is GameCode._Freeze301
                or GameCode._Freeze501
                or GameCode._Freeze701
                or GameCode._Freeze901
                or GameCode._Freeze1101
                or GameCode._Freeze1501;
        }

        public static bool IsKickDown(this GameCode code)
        {
            return code is GameCode._KickDown301
                or GameCode._KickDown501;
        }

        public static bool IsCountUp(this GameCode code)
        {
            return code is GameCode._Countup;
        }

        public static bool IsCR(this GameCode? code)
        {
            return code != null && code.Value.IsCR();
        }

        public static bool IsCR(this GameCode code)
        {
            return code is GameCode._StandardCR
                or GameCode._HiddenCR
                or GameCode._CutThroat
                or GameCode._HiddenCutThroat
                or GameCode._WildCardCR
                or GameCode._TeamCR
                or GameCode._ANIMAL_STANDARD_CR;
        }

        public static bool IsCRWithoutAnimal
        (
            this GameCode code
        )
        {
            return code is GameCode._StandardCR
                or GameCode._HiddenCR
                or GameCode._CutThroat
                or GameCode._HiddenCutThroat
                or GameCode._WildCardCR
                or GameCode._TeamCR;
        }

        public static bool IsCRWithoutAnimal
        (
            this GameCode? code
        )
        {
            return code.Value.IsCRWithoutAnimal();
        }

        public static bool IsAnimalCR
        (
            this GameCode? code
        )
        {
            return code is GameCode._ANIMAL_STANDARD_CR;
        }

        public static bool IsPractice(this GameCode code)
        {
            return code is GameCode._Countup
                or GameCode._CrCountUp
                or GameCode._HalfIt
                or GameCode._DeltaShoot
                or GameCode._TargetHat
                or GameCode._Oniren
                or GameCode._TargetBull
                or GameCode._Target20
                or GameCode._ShootForce
                or GameCode._TargetHorse
                or GameCode._MultipleCr
                or GameCode._Pirates
                or GameCode._Spider
                or GameCode._Rotation;
        }

        public static bool IsPractice(this GameCode? code)
        {
            if (code == null) return false;
            return code.Value.IsPractice();
        }

        public static bool IsAnalyzablePracticeGame(this GameCode? code)
        {
            if (code == null) return false;
            return code.Value.IsAnalyzablePracticeGame();
        }

        public static bool IsAnalyzablePracticeGame(this GameCode code)
        {
            return code is GameCode._Countup
                or GameCode._CrCountUp
                or GameCode._HalfIt
                or GameCode._DeltaShoot
                or GameCode._TargetBull
                or GameCode._Target20
                or GameCode._Rotation;
        }
        public static bool NoNeedToSendStartLocalPlay
        (
            this GameCode code
        )
        {
            return code is GameCode._HyperBull
                or GameCode._TreasureHunt
                or GameCode._HideAndSeek
                or GameCode._2Line
                or GameCode._TicTacToe
                or GameCode._FunMission
                or GameCode._BaseBall;
        }

        public static bool NoNeedToSendStartLocalPlay
        (
            this GameCode? code
        )
        {
            if (code == null) return false;
            return code.Value.NoNeedToSendStartLocalPlay();
        }

        public static GameCode RepresentativeCodeForBackground(this GameCode code)
        {
            //代表のGameCodeでリクエストします。
            if (code.Is01() || code.IsFreeze01())
            {
                return GameCode._301;
            }
            else if (code.IsCR())
            {
                return GameCode._StandardCR;
            }
            else if (code.IsPractice())
            {
                return GameCode._Countup;
            }

            return code;
        }

        public static GameCode RepresentativeCodeForHitSound(this GameCode code)
        {
            if (code.Is01()) return GameCode._301;
            if (code.IsCR()) return GameCode._StandardCR;
            switch (code)
            {
                case GameCode._CrCountUp:
                case GameCode._TargetHorse:
                    return GameCode._StandardCR;
                default: return GameCode._301;
            }
        }

        public static GameCode RepresentativeCodeForHitSound(this GameCode? code)
        {
            if (code == null) return GameCode._301;
            return code.Value.RepresentativeCodeForHitSound();
        }

        public static bool IsAnimal(this GameCode? code)
        {
            if (code == null) return false;
            return code.Value.IsAnimal();
        }

        public static bool IsAnimal(this GameCode code)
        {
            return code is GameCode._ANIMAL_301GAME
                or GameCode._ANIMAL_501GAME
                or GameCode._ANIMAL_701GAME
                or GameCode._ANIMAL_901GAME
                or GameCode._ANIMAL_1101GAME
                or GameCode._ANIMAL_1501GAME
                or GameCode._ANIMAL_STANDARD_CR;
        }

        public static bool IsParty(this GameCode code)
        {
            return code is GameCode._BeyondTop
                or GameCode._HyperBull
                or GameCode._TreasureHunt
                or GameCode._2Line
                or GameCode._HideAndSeek
                or GameCode._FunMission
                or GameCode._TicTacToe
                or GameCode._KickDown301
                or GameCode._KickDown501
                or GameCode._BaseBall;
        }

        public static bool IsParty(this GameCode? code)
        {
            if (code == null) return false;
            return code.Value.IsParty();
        }

        public static bool Only1UnitRequiredToStart
        (
            this GameCode? code
        ) => code.IsAnimal();

        public static bool Only2UnitsRequiredToStart
        (
            this GameCode? code
        )
        {
            return code is GameCode._2Line or GameCode._TicTacToe;
        }

        public static bool IsTeamGame
        (
            this GameCode? code
        )
        {
            return code is GameCode._TeamCR || code.IsFreeze01();
        }

        public static bool LessThan2UnitRequiredToStart
        (
            this GameCode? code
        )
        {
            return code is GameCode._FunMission;
        }

        public static bool LessThan4UnitsRequiredToStart
        (
            this GameCode? code
        )
        {
            return code is GameCode._TreasureHunt;
        }

        public static bool MoreThan2UnitsRequiredToStart
        (
            this GameCode? code
        )
        {
            return code is GameCode._HiddenCR
                or GameCode._BeyondTop
                or GameCode._HideAndSeek
                or GameCode._KickDown301
                or GameCode._KickDown501;
        }

        public static bool MoreThan3UnitsRequiredToStart
        (
            this GameCode? code
        )
        {
            return code is GameCode._CutThroat or GameCode._HiddenCutThroat;
        }

        public static bool HasOption
        (
            this GameCode? code
        )
        {
            return code is GameCode._301
                or GameCode._501
                or GameCode._701
                or GameCode._901
                or GameCode._1101
                or GameCode._1501
                or GameCode._ANIMAL_301GAME
                or GameCode._ANIMAL_501GAME
                or GameCode._ANIMAL_701GAME
                or GameCode._ANIMAL_901GAME
                or GameCode._ANIMAL_1101GAME
                or GameCode._ANIMAL_1501GAME
                or GameCode._Freeze301
                or GameCode._Freeze501
                or GameCode._Freeze701
                or GameCode._Freeze901
                or GameCode._Freeze1101
                or GameCode._Freeze1501
                or GameCode._StandardCR
                or GameCode._HiddenCR
                or GameCode._CutThroat
                or GameCode._HiddenCutThroat
                or GameCode._TeamCR
                or GameCode._WildCardCR
                or GameCode._Countup
                or GameCode._Rotation
                or GameCode._TargetHat
                or GameCode._MultipleCr
#if !GranPro
                or GameCode._TargetBull
                or GameCode._Target20
                or GameCode._TargetHorse
#endif
                or GameCode._Spider
                or GameCode._Pirates
                or GameCode._TicTacToe
                or GameCode._KickDown301
                or GameCode._KickDown501
                or GameCode._Oniren
                or GameCode._BaseBall;
        }

        public static string ShortName(this GameCode code)
        {
            return code switch
            {
                GameCode._301 => "301",
                GameCode._501 => "501",
                GameCode._701 => "701",
                GameCode._901 => "901",
                GameCode._1101 => "1101",
                GameCode._1501 => "1501",
                GameCode._Freeze301 => "Freeze 301",
                GameCode._Freeze501 => "Freeze 501",
                GameCode._Freeze701 => "Freeze 701",
                GameCode._Freeze901 => "Freeze 901",
                GameCode._Freeze1101 => "Freeze 1101",
                GameCode._Freeze1501 => "Freeze 1501",
                GameCode._StandardCR => "St.CR.",
                _ => throw new NotSupportedException($"ShortName not support {code}")
            };
        }

        public static string ShortName(this GameCode? code)
        {
            if (code == null) return "CHOICE";
            return ShortName(code.Value);
        }

        public static string OnlineRequestGameName(this GameCode? code)
        {
            if (code == null)
            {
                return "CHOICE";
            }
            else
            {
                switch (code)
                {
                    case GameCode._301: return "301";
                    case GameCode._501: return "501";
                    case GameCode._701: return "701";
                    case GameCode._901: return "901";
                    case GameCode._1101: return "1101";
                    case GameCode._1501: return "1501";
                    case GameCode._StandardCR: return "CR";
                    case GameCode._CutThroat: return "CUT";
                    default: throw new NotSupportedException($"ShortName not support {code}");
                }
            }
        }

        public static string LongName(this GameCode code)
        {
            switch (code)
            {
                case GameCode._301: return "301 Game";
                case GameCode._501: return "501 Game";
                case GameCode._701: return "701 Game";
                case GameCode._901: return "901 Game";
                case GameCode._1101: return "1101 Game";
                case GameCode._1501: return "1501 Game";
                case GameCode._Freeze301: return "Freeze 301";
                case GameCode._Freeze501: return "Freeze 501";
                case GameCode._Freeze701: return "Freeze 701";
                case GameCode._Freeze901: return "Freeze 901";
                case GameCode._Freeze1101: return "Freeze 1101";
                case GameCode._Freeze1501: return "Freeze 1501";
                case GameCode._StandardCR: return "StandardCR";
                case GameCode._CutThroat: return "CutThroatCR";
                case GameCode._TeamCR: return "Team CR.";
                case GameCode._Countup: return "CountUp";
                case GameCode._CrCountUp: return "CR.CountUp";
                case GameCode._HalfIt: return "Half It";
                case GameCode._DeltaShoot: return "Delta Shoot";
                case GameCode._TargetBull: return "Target Bull";
                case GameCode._Target20: return "Target 20";
                case GameCode._TargetHat: return "Target Hat";
                case GameCode._TargetHorse: return "Target Horse";
                case GameCode._Rotation: return "Rotation";
                case GameCode._KickDown301:
                case GameCode._KickDown501: return "KICK DOWN";
                case GameCode._WildCardCR: return "WildCard CR.";
                case GameCode._Spider: return "Spider";
                case GameCode._FunMission: return "Fun Mission";
                case GameCode._2Line: return "Two Line Battle";
                case GameCode._MultipleCr: return "Multiple CR.";
                case GameCode._TreasureHunt: return "Treasure Hunt";
                case GameCode._ShootForce: return "Shoot Force";
                case GameCode._Oniren: return "Oniren";
                case GameCode._Pirates: return "Pirates";
                case GameCode._BeyondTop: return "Beyond Top";
                case GameCode._HyperBull: return "Hyper Bull";
                case GameCode._HideAndSeek: return "Hide And Seek";
                case GameCode._TicTacToe: return "Tic Tac Toe";
                case GameCode._ANIMAL_301GAME: return "Animal 301 Game";
                case GameCode._ANIMAL_501GAME: return "Animal 501 Game";
                case GameCode._ANIMAL_701GAME: return "Animal 701 Game";
                case GameCode._ANIMAL_901GAME: return "Animal 901 Game";
                case GameCode._ANIMAL_1101GAME: return "Animal 1101 Game";
                case GameCode._ANIMAL_1501GAME: return "Animal 1501 Game";
                case GameCode._ANIMAL_STANDARD_CR: return "Animal StandardCR";
                case GameCode._HiddenCR: return "Hidden Cricket";
                case GameCode._HiddenCutThroat: return "Hidden Cut Throat";
                case GameCode._TargetChoice: return "Target Choice";
                case GameCode._BaseBall: return "Base Ball";
                default: throw new NotSupportedException($"LongName not support {code}");
            }
        }


        public static bool Is01(this GameCode?[] codes)
        {
            return codes.Length == 1 && codes[0].Is01();
        }

        public static bool IsCr(this GameCode?[] codes)
        {
            return codes.Length == 1 && codes[0].IsCR();
        }

        public static bool IsMedley(this GameCode?[] codes)
        {
            return codes.Length > 1;
        }

        public static bool IsAnimalGame
        (
            this GameCode?[] codes
        )
        {
            return codes.First().IsAnimal();
        }

        public static bool IsAnimalMedley
        (
            this GameCode?[] codes
        )
        {
            //Choiceもあるので、Nullがあります。
            return codes.IsMedley() && codes.Any(c => c.IsAnimal());
        }

        public static int MedleyLegCount(this GameCode?[] codes)
        {
            return codes.IsMedley() ? codes.Length : 0;
        }

        public static ZeroOneEndScore ToEndScore
        (
            this GameCode? code
        )
        {
            if (code == null) throw new ArgumentException($"EndScore is not supported for this game. {code}");
            return code.Value.ToEndScore();
        }

        public static ZeroOneEndScore ToEndScore
        (
            this GameCode code
        )
        {
            return code switch
            {
                GameCode._301 => ZeroOneEndScore._301,
                GameCode._501 => ZeroOneEndScore._501,
                GameCode._701 => ZeroOneEndScore._701,
                GameCode._901 => ZeroOneEndScore._901,
                GameCode._1101 => ZeroOneEndScore._1101,
                GameCode._1501 => ZeroOneEndScore._1501,
                GameCode._ANIMAL_301GAME => ZeroOneEndScore._301,
                GameCode._ANIMAL_501GAME => ZeroOneEndScore._501,
                GameCode._ANIMAL_701GAME => ZeroOneEndScore._701,
                GameCode._ANIMAL_901GAME => ZeroOneEndScore._901,
                GameCode._ANIMAL_1101GAME => ZeroOneEndScore._1101,
                GameCode._ANIMAL_1501GAME => ZeroOneEndScore._1501,
                GameCode._Freeze301 => ZeroOneEndScore._301,
                GameCode._Freeze501 => ZeroOneEndScore._501,
                GameCode._Freeze701 => ZeroOneEndScore._701,
                GameCode._Freeze901 => ZeroOneEndScore._901,
                GameCode._Freeze1101 => ZeroOneEndScore._1101,
                GameCode._Freeze1501 => ZeroOneEndScore._1501,
                GameCode._KickDown301 => ZeroOneEndScore._301,
                GameCode._KickDown501 => ZeroOneEndScore._501,
                _ => throw new ArgumentException($"EndScore is not supported for this game. {code}")
            };
        }

        public static IGameRule CreateDefaultGameRule
        (
            this GameCode? code
        )
        {
            return code switch
            {
                null => new ChoiceGameRule(),
                GameCode._301 => new GameRule01(code.Value),
                GameCode._501 => new GameRule01(code.Value),
                GameCode._701 => new GameRule01(code.Value),
                GameCode._901 => new GameRule01(code.Value),
                GameCode._1101 => new GameRule01(code.Value),
                GameCode._1501 => new GameRule01(code.Value),
                GameCode._Freeze301 => new GameRuleFreeze01(code.Value),
                GameCode._Freeze501 => new GameRuleFreeze01(code.Value),
                GameCode._Freeze701 => new GameRuleFreeze01(code.Value),
                GameCode._Freeze901 => new GameRuleFreeze01(code.Value),
                GameCode._Freeze1101 => new GameRuleFreeze01(code.Value),
                GameCode._Freeze1501 => new GameRuleFreeze01(code.Value),
                GameCode._KickDown301 => new GameRuleKickDown(code.Value),
                GameCode._KickDown501 => new GameRuleKickDown(code.Value),
                GameCode._StandardCR => new GameRuleStandardCR(),
                GameCode._HiddenCR => new GameRuleStandardCR(markDisplayOption: CricketMarkDisplayOption.Hidden),
                GameCode._CutThroat => new GameRuleCutThroat(),
                GameCode._HiddenCutThroat => new GameRuleCutThroat(markDisplayOption: CricketMarkDisplayOption.Hidden),
                GameCode._TeamCR => new GameRuleTeamCR(),
                GameCode._WildCardCR => new GameRuleWildCardCR(),
                GameCode._Countup => new GameRuleCountUp(),
                GameCode._CrCountUp => new GameRuleCRCountUp(),
                GameCode._HalfIt => new GameRuleHalfIt(),
                GameCode._DeltaShoot => new GameRuleDeltaShoot(),
                GameCode._TargetHat => new GameRuleTargetHat(),
                GameCode._TargetChoice =>
                    throw new NotSupportedException("TargetChoice is not support."), //new GameRuleTargetChoice(
                GameCode._Oniren => new GameRuleOniren(),
                GameCode._TargetBull => new GameRuleTargetBull(),
                GameCode._Target20 => new GameRuleTarget20(),
                GameCode._ShootForce => new GameRuleShootForce(),
                GameCode._TargetHorse => new GameRuleTargetHorse(),
                GameCode._MultipleCr => new GameRuleMultipleCR(),
                GameCode._Pirates => new GameRulePirates(),
                GameCode._Spider => new GameRuleSpider(),
                GameCode._Rotation => new GameRuleRotation(),
                GameCode._BeyondTop => new GameRuleBeyondTop(),
                GameCode._HyperBull => new GameRuleHyperBull(),
                GameCode._TreasureHunt => new GameRuleTreasureHunt(),
                GameCode._2Line => new GameRuleTwoLineBattle(),
                GameCode._HideAndSeek => new GameRuleHideAndSeek(),
                GameCode._FunMission => new GameRuleFunMission(),
                GameCode._TicTacToe => new GameRuleTicTacToe(),
                GameCode._ANIMAL_301GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_501GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_701GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_901GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_1101GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_1501GAME => new GameRule01(code.Value),
                GameCode._ANIMAL_STANDARD_CR => new GameRuleStandardCR(),
                GameCode._BaseBall => new GameRuleBaseBall(),
                _ => throw new ArgumentOutOfRangeException(nameof(code), code, null)
            };
        }
    }

}