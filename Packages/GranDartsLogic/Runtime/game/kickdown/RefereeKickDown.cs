using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandartslogic.extentions;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.domain.service;
using System.Numerics;
using com.luxza.grandartslogic.game.round.components.analyzer;

namespace com.luxza.grandartslogic.domain.game.KickDown
{
    public sealed class RefereeKickDown : BaseReferee<MatchKickDown, MatchScorerKickDown, GameKickDownEventPublisher>
    {
        internal RefereeKickDown(
            MatchKickDown match
        ) : base(
            match,
            new MatchScorerKickDown(match))
        {
        }

        //ゲームの終了条件を達成したかを返す　以下のどちらかの条件を満たすとtrue
        //　目標のスコアになった人がいるチームがでた
        //　最終ラウンドかつ投げ切った
        public override bool IsReachGameEnd
        {
            get
            {
                return Participants.AllUnits.Any(unit => IsGameEndScore(unit.Id)) || IsReachGameEndLastThrow;
            }
        }

        //チェンジした時に呼ばれる
        //最大ラウンドを超えてるならゲーム終了
        public override bool IsNextRoundOverMaxRound => Match.IsNextRoundToOverMaxRound;


        //最終ラウンドかつ投げ切ったかを返す
        private bool IsReachGameEndLastThrow => IsNextRoundOverMaxRound && IsThrowCountOverMaxThrowInRound;

        public override void ReStartGame()
        {
            Scorer = new MatchScorerKickDown(Match);
            base.ReStartGame();
        }

        public void RecalculationGame()
        {
            List<Segment> thisGameAllSegments = new List<Segment>();
            //Debug.Log("CurrentTeamLatestRound().No:" + CurrentTeamLatestRound().No);
            for (var roundIndex = 0; roundIndex < CurrentTeamLatestRound().No; roundIndex++)
            {

                foreach (var unit in Match.ParticipantTeams.AllUnits)
                {
                    if (roundIndex >= unit.Progress.Rounds.Count) continue;
                    var round = unit.Progress.Round(roundIndex + 1);

                    if(round.InputType != RoundInputType.Segment) throw new InvalidOperationException("Round input type is not segment.");

                    foreach (var hit in round.GetRoundComponent<SegmentInput>().Throws)
                    {
                        thisGameAllSegments.Add(hit.ActuaryHitArea);
                    }
                }
            }

            Participants.ResetUnitData();

            _currentThrowingUnitIndex = 0;

            ReStartGame(thisGameAllSegments.ToArray(), false);
        }

        protected override void StartRoundAtCurrentTeam()
        {
            var round = CurrentUnit.StartRound();
            round.GetRoundComponent<KickdownAdditionalRound>().DecideDecreasePointWhenBust(Match.Rule.OverScoreSetting);
        }

        private void ReStartGame(Segment[] segments, bool isNeedLastChange)
        {
            //通知機能をオフにするため、一回ActionDelegateを外す
            EventPublisher.Enabled = false;
            StartRoundAtCurrentTeam();

            //segmentのリストは、必ず抜けている箇所がない前提で進む
            //例えば、(Round1の2投目はあるけど、1投目がないなどはない前提）
            for (int i = 0; i < segments.Length; i++)
            {
                //通常のゲームでも呼ばれるHitの処理
                //Segmentがnullかmissならヒットの処理を呼ばないmissはOverScoreか投げずにチェンジくらいしかあり得ない
                if (segments[i] != null)
                {
                    if (!segments[i].IsMiss) AcceptHit(segments[i], System.Numerics.Vector2.Zero);
                }

                if (i == segments.Length - 1)
                {
                    //最後のKeyの処理
                    //３投目の場合、Changeするかどうかを決める
                    if (IsThrowCountOverMaxThrowInRound && isNeedLastChange)
                    {
                        ChangeToNextTeam();
                    }
                    break;
                }
                if (IsThrowCountOverMaxThrowInRound)
                {
                    ChangeToNextTeam();
                }
            }

            EventPublisher.Enabled = true;
            if (IsReachGameEnd)
            {
                GameEnd();
            }
        }

        public bool IsGameEndScore(string unitId) => IsGameEndScore(Scorer.CurrentScore(unitId), Match.Rule.EndScore.AsInt());

        private bool IsGameEndScore(int score, int endScore) => score == endScore;

        public override int CurrentScore(string unitId) => Scorer.CurrentScore(unitId);

        public bool AnyUnitHasReachedEndScore => Participants.AllUnits.Any(unit => IsGameEndScore(unit.Id));


        public override void AcceptHit(Segment segment, System.Numerics.Vector2? hitPosition = null)
        {
            if (IsGameEndScore(CurrentThrowingUnitId) || CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().IsBust)
            {
                //Debug.LogError("Receive key. but already finished!");
                return;
            }
            var virtualHit = IsSatisfiedInConditions(segment) ? segment : Segment.Miss;
            Match.StoreHit(_currentThrowingUnitIndex, segment, virtualHit, hitPosition);

            var currentUnitScore = Scorer.CurrentScore(CurrentThrowingUnitId);
            if (currentUnitScore > Match.Rule.EndScore.AsInt())
            {
                OverScore();
                return;
            }
            if (currentUnitScore == Match.Rule.EndScore.AsInt())
            {
                if (!IsSatisfiedOutConditions(virtualHit))
                {
                    OverScore();
                    return;
                }
            }
            else if (Match.Rule.EndScore.AsInt() - currentUnitScore == 1)
            {
                switch (Match.Rule.OutCondition)
                {
                    case OutCondition.DoubleOut:
                    case OutCondition.MasterOut:
                        OverScore();
                        return;
                }
            }

            if (Scorer.CheckKickDown(CurrentThrowingUnitId, CurrentUnitProgress.CurrentRound))
            {
                EventPublisher.PublishKickDown();
            }

            Scorer.CalculateTeamRealTimeStats(CurrentUnit);
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            if (Match.IsReachEndOfRound(_currentThrowingUnitIndex) || IsGameEndScore(currentUnitScore, Match.Rule.EndScore.AsInt()))
            {
                var award = AchievedAward(CurrentRoundAtCurrentTeam);
                EventPublisher.PublishAchieveAward(award, CurrentUnit);
                if (IsReachEightyPercent())
                {
                    Confirm80PercentStatsIfNotConfirmed();
                }
                EventPublisher.PublishEndTurn(CurrentUnit);
            }

            EventPublisher.PublishUpdateProgress(CurrentUnit);
            if (IsGameEndScore(CurrentThrowingUnitId) || IsReachGameEndLastThrow)
            {
                GameEnd();
            }
        }

        public override void AcceptScore
        (
            int score,
            int checkout,
            int checkoutTry
        )
        {
            throw new NotSupportedException("Kickdown does not support vist input.");
        }

        public override void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
            else
            {
                if (IsReachEightyPercent())
                {
                    Confirm80PercentStatsIfNotConfirmed();
                }
                //リアルタイムスタッツを再計算
                Scorer.CalculateTeamRealTimeStats(CurrentUnit);
                var sender = CurrentUnit;
                _currentThrowingUnitIndex = NextTeamIndex(_currentThrowingUnitIndex);
                StartRoundAtCurrentTeam();
                EventPublisher.PublishChange(sender);
            }
        }

        public override void OverrideThrow(int throwNo, Segment segment)
        {
            if (CurrentUnitProgress.CurrentRound.InputType != RoundInputType.Segment)
            {
                throw new InvalidOperationException("This operation is only available for SegmentInput.");
            }

            EventPublisher.Enabled = false;
            if (CurrentRoundAtCurrentTeam.TryGetRoundComponent<OverwriteCounter>(out var overwriteCounter))
            {
                overwriteCounter.MarkOverwrite(throwNo);
            }
            var sender = CurrentUnit;
            //現在の投擲をコピーしておく。後で残りのThrowを復元するためです。
            Throw[] src = CurrentUnitProgress.CurrentRound.GetRoundComponent<SegmentInput>().Throws.ToArray();
            //修正したいThrowまで一旦戻す.
            while (CurrentUnitProgress.CurrentRound.ThrowCount >= throwNo)
            {
                RevertCurrentThrow();
            }

            if (CurrentUnitProgress.CurrentRound.TryGetRoundComponent<IBustController>(out var bust))
            {
                //BUSTの場合、Roundの最初から入れ直していく必要があります。VirtualHitの更新がされてしまっているためです。
                CurrentUnitProgress.ResetCurrentRound();
                if (throwNo >= 1)
                {
                    foreach (var item in src.Take(throwNo - 1))
                    {
                        AcceptHit(item.ActuaryHitArea, item.HitPosition);
                    }
                }
            }

            //新しい投擲を入れます。
            AcceptHit(segment, null);
            //残りのKeyはもとのままで復帰します。
            foreach (var item in src.Skip(throwNo))
            {
                // 投げていないかBUSTで埋まったOUTが入っているのであれば復帰処理は終了します。
                if (item.IsEmpty || item.ActuaryHitArea == Segment.OUT) break;
                AcceptHit(item.ActuaryHitArea, item.HitPosition);
            }

            EventPublisher.Enabled = true;
            if (CurrentUnitProgress.CurrentRound.TryGetRoundComponent<KickdownAdditionalRound>(out var kickdownAdditionalRound))
            {
                //kickdownの場合はKickedCountかKickDownカウントをリセットして再度この投擲はKickDownだったのか判定させる
                foreach (var unit in Match.ParticipantTeams.AllUnits)
                {
                    if (unit.Id.Equals(CurrentUnit.Id))
                    {
                        continue;
                    }
                    var round = unit.Progress.Rounds.FirstOrDefault(r => r.No == CurrentUnitProgress.CurrentRoundNo);
                    if (round != null && !round.IsEmptyRound)
                    {
                        round.GetRoundComponent<KickdownAdditionalRound>().ResetKickedBy();
                    }
                }
                kickdownAdditionalRound.ResetKickTo();
                if (Scorer.CheckKickDown(CurrentThrowingUnitId, CurrentUnitProgress.CurrentRound))
                {
                    EventPublisher.PublishKickDown();
                }
            }

            EventPublisher.PublishUpdateProgress(sender);

            if (IsReachGameEnd)
            {
                GameEnd();
            }
        }

        private int NextTeamIndex(int currentTeamIndex) => (currentTeamIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : currentTeamIndex + 1;

        protected override void GameEnd()
        {
            //100%に達した時、80%Statsの計算をしていない場合がある。
            //80%を通り越して、100%(ゲーム終了）となってしまう場合を考慮する。
            Confirm80PercentStatsIfNotConfirmed();
            Confirm100PercentStats();

            Scorer.CreatePlaydata();
            MatchFinishStatus matchFinishStatus = CalculateRanking();
            EventPublisher.PublishFinishMatch(matchFinishStatus);
        }

        //OverScoreが発生した時の処理
        //そのラウンドを強制終了する仕組み上は01のBustと同じなのでbustのイベント通知を使ってる
        private void OverScore()
        {
            CurrentRoundAtCurrentTeam.GetRoundComponent<IBustController>().Bust();
            EventPublisher.PublishBust();
            EventPublisher.PublishUpdateProgress(CurrentUnit);
            EventPublisher.PublishEndTurn(CurrentUnit);
            if (IsNextRoundOverMaxRound)
            {
                GameEnd();
            }
        }

        protected override MatchFinishStatus CalculateRanking()
        {
            if (Participants.Count == 1) return new MatchFinishStatus(Participants.AllUnits[0]);

            DecideRanking(Participants.AllUnits);

            //もし全員のチームが同じ順位なら勝者は無しnullを返す
            //そうでない場合は勝者がいるので1位の人を返す
            return new MatchFinishStatus(GetWinner());
        }

        /// <summary>
        /// ランキングを決めます。
        /// 指定したUnitの配列と、何番目から割り振るかを指定します。
        /// </summary>
        /// <param name="units">Unitのリスト</param>
        /// <param name="to">何番目から割り振るか</param>
        private void DecideRanking(Unit[] units, int to = 1)
        {
            //ゲームの順位決めのロジック
            var ordered = units.OrderByDescending(team =>
            {
                //1.Score降順
                var score = Scorer.CurrentScore(team.Id);
                return score;
            }).ToList();
            //もし全てのUnitの最低スコアが同じならPPR80で順位決める
            if (ordered.All(team => Scorer.CurrentScore(team.Id) == Scorer.CurrentScore(ordered[0].Id)))
            {
                ordered = units.OrderByDescending(team =>
                {
                    //PPRFor80降順
                    var ppr80 = team.AllMember.Average(member => Scorer.PPRFor80(member.GranId));
                    return ppr80;
                }).ToList();
            }
            //並べ替えたリストにgameRankingの値を1~チーム数上限(最大8)の値を降っていく
            int rank = to;
            for (int i = 0; i < ordered.Count(); i++)
            {
                if (i == 0)
                {
                    Participants.Unit(ordered.ElementAt(i).Id).GameRanking = rank;
                    continue;
                }
                var currentTeamId = ordered.ElementAt(i).Id;

                Participants.Unit(currentTeamId).GameRanking = ++rank;
            }
            if (ordered.All(team => Scorer.CurrentScore(team.Id) == Scorer.CurrentScore(ordered[0].Id)) &&
                ordered.All(team => team.AllMember.Average(member => Scorer.PPRFor80(member.GranId)) == ordered[0].AllMember.Average(member => Scorer.PPRFor80(member.GranId))))
            {
                //もしスコアと80%スタッツが完全同点だった場合は
                //引き分けにするので全ユニットの順位を割り振り開始の数にする
                for (int i = 0; i < ordered.Count(); i++)
                {
                    ordered.ElementAt(i).GameRanking = to;
                }
            }
        }

        public Unit GetWinner() => IsDrawGame() ? Participants.AllUnits.First(team => team.GameRanking == 1) : null;

        private void Confirm80PercentStatsIfNotConfirmed()
        {
            foreach (var team in Participants.AllUnits)
            {
                foreach (var player in team.AllMember)
                {
                    if (Scorer.PPRFor80(player.GranId).HasValue || Scorer.PPDFor80(player.GranId).HasValue)
                    {
                        //already confirmed.
                        continue;
                    }

                    var totalRoundScore = 0;
                    var MemberRounds = team.AllRoundsByMember(player.GranId);

                    foreach (var round in MemberRounds)
                    {
                        totalRoundScore += Scorer.TotalScoreInRounds(round);
                    }

                    int totalThrow = MemberRounds.Sum(r => r.ThrowCount);
                    var ppr = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)MemberRounds.Length;
                    var ppd = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)totalThrow;
                    Scorer.SetPPRFor80(player.GranId, ppr);
                    Scorer.SetPPDFor80(player.GranId, ppd);
                }
            }
        }

        private void Confirm100PercentStats()
        {
            foreach (var team in Participants.AllUnits)
            {
                foreach (var player in team.AllMember)
                {
                    if (Scorer.PPRFor100(player.GranId).HasValue || Scorer.PPDFor100(player.GranId).HasValue)
                    {
                        //already confirmed.
                        continue;
                    }

                    var totalRoundScore = 0;
                    var MemberRounds = team.AllRoundsByMember(player.GranId);

                    foreach (var round in MemberRounds)
                    {
                        totalRoundScore += Scorer.TotalScoreInRounds(round);
                    }

                    int totalThrow = MemberRounds.Sum(r => r.ThrowCount);

                    var ppr = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)MemberRounds.Length;
                    var ppd = MemberRounds.Length == 0 ? 0.00 : totalRoundScore / (double)totalThrow;
                    Scorer.SetPPRFor100(player.GranId, ppr);
                    Scorer.SetPPDFor100(player.GranId, ppd);
                }
            }
        }

        public override int TotalScore(Round round)
        {
            if (round.GetRoundComponent<IBustController>().IsBust) return 0;
            return base.TotalScore(round);
        }

        public bool IsAchieveInConditionAtCurrentTeam()
        {
            return Match.Rule.InCondition switch
            {
                InCondition.OpenIn => true,
                InCondition.DoubleIn => CurrentUnit.AllThrows().
                    Any(t => IsAchievableDoubleConditionKey(t.VirtualHitArea)),
                InCondition.MasterIn => CurrentUnit.AllThrows().
                    Any(t => IsAchievableMasterConditionKey(t.VirtualHitArea)),
                _ => true
            };
        }

        private bool IsAchievableDoubleConditionKey(Segment segment) => segment == null
            ? false
            : Match.IsEnableSeparateBull
                ? segment.IsDouble
                : segment.IsDouble || segment == Segment.BullOut;

        private bool IsAchievableMasterConditionKey(Segment segment) =>
            segment != null && (segment.IsDouble || segment.IsTriple || segment.IsBull);

        /// <summary>
        /// 開始条件のチェックをします。
        /// <see cref="GameStartConditions"/>
        /// </summary>
        /// <param name="segment">刺さった位置</param>
        /// <returns>刺さった位置が開始条件を満たすかどうか</returns>
        private bool IsSatisfiedInConditions(Segment segment)
        {
            if (!IsAchieveInConditionAtCurrentTeam())
            {
                switch (Match.Rule.InCondition)
                {
                    case InCondition.DoubleIn: return IsAchievableDoubleConditionKey(segment);
                    case InCondition.MasterIn: return IsAchievableMasterConditionKey(segment);
                }
            }

            return true;
        }

        private bool IsSatisfiedInConditions
        (
            int score
        )
        {
            if (Scorer.CurrentSubtractedScore(CurrentUnit.Id) == 0)
            {
                return Match.Rule.InCondition switch
                {
                    InCondition.OpenIn => true,
                    InCondition.DoubleIn => InOutConditionService.AchievableDoubleInOrOutScore(score),
                    InCondition.MasterIn => InOutConditionService.AchievableMasterInOrOutScore(score),
                    _ => true
                };
            }

            return true;
        }

        private bool IsSatisfiedOutConditions(Segment segment)
        {
            if (Scorer.CurrentSubtractedScore(CurrentUnit.Id) == 0)
            {
                return Match.Rule.OutCondition switch
                {
                    OutCondition.OpenOut => true,
                    OutCondition.DoubleOut => segment.IsDouble,
                    OutCondition.MasterOut => segment.IsDouble || segment.IsTriple || segment.IsBull,
                    _ => true
                };
            }

            return true;
        }



        public override int TotalScoreAtCurrentThrowingTeam => Scorer.CurrentScore(CurrentThrowingUnitId);

        public override IEnumerable<(Round round, int score)> RoundsAndScoresAt
        (
            string unitId
        )
        {
            return AllRoundsAt(unitId).Select(r => (r, TotalScore(r)));
        }


        public override void RevertCurrentRound()
        {
            if (CurrentUnitProgress.Rounds.Count == 1 && _currentThrowingUnitIndex == 0 &&
                CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
            {
                //1ラウンドの最初のチームが1投もしてない時にラウンドリバースした時は処理自体をしない
                //RoundのNoだと、Roundを継続できるゲームではうまく機能しない。Roundの数でみる。
                return;
            }

            try
            {
                EventPublisher.Enabled = false;
                var sender = CurrentUnit;
                var changedThrowingUnit = false;
                //もし現在のチームが1投もしてなければ
                //前のチームに戻す
                if (CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
                {
                    var discardsRoundNo = CurrentUnitProgress.CurrentRound.No;
                    //前のチームに戻るので現在のRoundを削除
                    CurrentUnitProgress.DiscardCurrentRound();


                    //前のチームインデックスに戻す
                    //1引いた数値を入れ直す。最初のチームだった場合は最後のチームのインデックスにする。
                    _currentThrowingUnitIndex = (_currentThrowingUnitIndex - 1 < 0)
                        ? Match.ParticipantTeams.Count - 1
                        : _currentThrowingUnitIndex - 1;

                    changedThrowingUnit = true;

                }

                PreRevertCurrentRound();
                //現在のラウンドのデータをリセットして通知
                CurrentUnitProgress.ResetCurrentRound();
                ResetRoundData();
                RefreshGameData();

                RecalculationGame();
                EventPublisher.Enabled = true;
                EventPublisher.PublishRoundReverse(sender);
                if (changedThrowingUnit)
                {
                    EventPublisher.PublishChange(sender);
                }
            }
            finally
            {
                EventPublisher.Enabled = true;
            }
        }


        public override void RefreshGameData()
        {
            //80%に満たない場合、Statsもリセットする
            if (!IsReachEightyPercent())
            {
                Scorer.DiscardStats();
            }

            //リアルタイムスタッツを再計算
            Scorer.CalculateTeamRealTimeStats(CurrentUnit);

        }


        public override Award[] AchievableAward => GameRuleKickDown.AchievableAward;

        protected override void ResetRoundData()
        {
            Scorer.ResetScore(CurrentUnit.Id, CurrentUnitProgress.CurrentRoundNo - 1, 0);

            // Set current round as valid round when reverting other players' rounds
            foreach (var unit in Match.ParticipantTeams.AllUnits)
            {
                if (unit.Id.Equals(CurrentUnit.Id))
                {
                    continue;
                }
                var round = unit.Progress.Rounds.FirstOrDefault(r => r.No == CurrentUnitProgress.CurrentRoundNo);
                if (round != null && !round.IsEmptyRound)
                {
                    round.GetRoundComponent<KickdownAdditionalRound>().ResetKickedBy();
                    round.GetRoundComponent<KickdownAdditionalRound>().ResetKickTo();
                }
            }
        }

        public override void PreRevertCurrentThrow()
        {
            base.PreRevertCurrentThrow();
            // Set current round as valid round when reverting other players' rounds
            foreach (var unit in Match.ParticipantTeams.AllUnits)
            {
                if (unit.Id.Equals(CurrentUnit.Id))
                {
                    continue;
                }
                if (!unit.Progress.HasRounds) continue;
                var round = unit.Progress.LatestRound();
                if (!round.IsEmptyRound)
                {
                    var kickdownRound = round.GetRoundComponent<KickdownAdditionalRound>();
                    if (kickdownRound.IsKickedBy(CurrentUnit.Id))
                    {
                        kickdownRound.ResetKickedBy();
                        kickdownRound.ResetKickTo();
                    }
                }
            }
        }

        protected override void ResetThrowData()
        {
            Scorer.ResetScore(CurrentUnit.Id, CurrentUnitProgress.CurrentRoundNo - 1, CurrentUnitProgress.CurrentThrowNo);
        }

        /// <summary>
        /// 前のチームインデックスに戻す
        /// 1引いた数値を入れ直す。最初のチームだった場合は最後のチームのインデックスにする。
        /// </summary>
        /// <param name="currentIndex"></param>
        /// <param name="targetRoundNo"></param>
        /// <returns></returns>
        private int RevertUnitIndex(int currentIndex, int targetRoundNo) => (currentIndex - 1 < 0)
                                                                                      ? Match.ParticipantTeams.Count - 1
                                                                                      : currentIndex - 1;

        private bool IsReachEightyPercent() => Participants.AllUnits.Any(team => Match.Rule.EndScore.EightyPercentScore() >= Scorer.CurrentScore(team.Id));

        /// <summary>
        /// 这个游戏添加一个bust的话不计算奖励的处理
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        /// <exception cref="InvalidCodeException"></exception>
        protected override Award[] AchievedAward(Round round)
        {
            if (round.GetRoundComponent<IBustController>().IsBust)
            {
                return new List<Award>().ToArray();
            }
            return base.AchievedAward(round);
        }

        public override List<int> GetScoreListOfRound(Round round)
        {
            if (round.GetRoundComponent<IBustController>().IsBust) return new List<int>();
            return base.GetScoreListOfRound(round);
        }
    }
}