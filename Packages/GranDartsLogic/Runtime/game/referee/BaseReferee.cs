using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.award;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.domain.progress;
using com.luxza.grandartslogic.domain.round.snapshot;
using com.luxza.grandartslogic.extentions;
using com.luxza.grandartslogic.game.round.components.analyzer;
using com.luxza.grandartslogic.interfaces;
using com.luxza.granlog;
using UnityEngine;

namespace com.luxza.grandartslogic.domain
{
    public abstract class BaseReferee<TMatch, TScorer, TGameEventPublisher>
        : IReferee<TMatch, TScorer, TGameEventPublisher>
        where TMatch : IMatch<IGameRule>
        where TScorer : IMatchScorer
        where TGameEventPublisher : GameEventPublisher, new()
    {
        protected int _currentThrowingUnitIndex;
        public TGameEventPublisher EventPublisher { get; } = new();

        protected UnitProgress CurrentUnitProgress => CurrentUnit.Progress;
        protected Unit CurrentUnit => Match.ParticipantTeams.Unit(_currentThrowingUnitIndex);

        private bool FindUnit(string unitId, out Unit unit)
        {
            foreach (var u in Match.ParticipantTeams.AllUnits)
            {
                if (u.Id.Equals(unitId))
                {
                    unit = u;
                    return true;
                }
            }

            unit = null;
            return false;
        }

        public abstract bool IsReachGameEnd
        {
            get;
        }

        public abstract bool IsNextRoundOverMaxRound
        {
            get;
        }

        public TMatch Match { get; }
        public TScorer Scorer { get; protected set; }

        public virtual void ReStartGame()
        {
            Participants.ResetUnitData();
            StartMatch();
        }

        protected BaseReferee(TMatch match, TScorer scorer)
        {
            Match = match;
            Scorer = scorer;
        }

        protected virtual void PreActionStartMatch() {

        }

        protected virtual void PostActionStartMatch() {

        }

        public virtual void StartMatch()
        {
            _currentThrowingUnitIndex = 0;
            StartRoundAtCurrentTeam();
            PreActionStartMatch();
            EventPublisher.PublishStartMatch();
            PostActionStartMatch();
        }

        public virtual void StartMatch(SnapShotItem[] snapShot)
        {
            EventPublisher.Enabled = false;
            StartMatch();

            //segmentのリストは、必ず抜けている箇所がない前提で進む
            //例えば、(Round1の2投目はあるけど、1投目がないなどはない前提）
            for (int i = 0; i < snapShot.Length; i++)
            {
                if (snapShot[i].IsChangeKey)
                {
                    ChangeToNextTeam();
                    continue;
                }
                if (snapShot[i].IsSegmentInputItem)
                {
                    AcceptHit(snapShot[i].Segment, snapShot[i].HitPosition);
                }
                else if (snapShot[i].IsVisitInputItem)
                {
                    AcceptScore(snapShot[i].Score, snapShot[i].CheckOut, 0);
                }
                if (CurrentRoundAtCurrentTeam.TryGetRoundComponent<IBustController>(out var bust))
                {
                    if (bust.IsBust)
                    {
                        //Bustした場合、残りがすべてMissになるので、その分をスキップします。
                        while (i < snapShot.Length && !snapShot[i].IsChangeKey)
                        {
                            i++;
                        }
                    }
                }
            }

            //ActionDelegateを入れ直す
            EventPublisher.Enabled = true;
            Log.d($"RecoveryCompleted. => roundNo: {CurrentRoundAtCurrentTeam.No} thrower: {Thrower.GranId} throwNo: {CurrentRoundAtCurrentTeam.ThrowCount}");
            EventPublisher.PublishRecovery();
            if (IsReachGameEnd)
            {
                GameEnd();
            }
        }

        public void RecoveryMatch(SnapShotItem[] snapShots)
        {
            Participants.ResetUnitData();
            _currentThrowingUnitIndex = 0;
            StartMatch(snapShots);
        }

        public void ShuffleThrowOrder()
        {
            Match.ParticipantTeams.Shuffle();
            EventPublisher.PublishChangeUnitThrowOrder();
        }

        public abstract int TotalScoreAtCurrentThrowingTeam
        {
            get;
        }

        public abstract int CurrentScore(string unitId);
        public int CurrentTeamScore
        (
            int teamTag
        )
        {
            return Match.ParticipantTeams.AllUnitsGroupByTeamTag.First(g => g.Key == teamTag).Sum(u => CurrentScore(u.Id));
        }
        public abstract void RefreshGameData();
        public abstract void AcceptHit(Segment segment, System.Numerics.Vector2? hitPosition);
        public virtual void AcceptScore(int score, int checkout, int checkoutTry) { }

        protected abstract void GameEnd();

        public Dictionary<Round, int> RoundsAtCurrentTeamWithRoundScore => RoundsAndScoresAt
                (CurrentThrowingUnitId).
            ToDictionary(v => v.round, v => v.score);


        public abstract IEnumerable<(Round round, int score)> RoundsAndScoresAt
        (
            string unitId
        );

        public int CalculateScore(Throw th)
        {
            if (th.IsEmpty || th.VirtualHitArea == null)
            {
                return 0;
            }

            return Scorer.Score(th.VirtualHitArea);
        }

        public int ParticipantsCount => Match.ParticipantTeams.Count;

        public virtual void ChangeToNextTeam()
        {
            EndRoundAtCurrentTeam();
            FixRoundAtCurrentUnit();
            var sender = CurrentUnit;
            _currentThrowingUnitIndex = (_currentThrowingUnitIndex + 1 >= Match.ParticipantTeams.Count) ? 0 : _currentThrowingUnitIndex + 1;
            StartRoundAtCurrentTeam();
            EventPublisher.PublishChange(sender);
        }

        public virtual void DiscardCurrentRoundAllInput() { }

        public string CurrentThrowingUnitId => CurrentUnit.Id;

        public int CurerntThrowingUnitIdAsInt => CurrentUnit.IdAsInt;

        public int CurrentThrowingTeamTag => CurrentUnit.TeamTag;

        public virtual bool IsThrowCountOverMaxThrowInRound =>
            Match.TeamThrowCountOnLatestRound(_currentThrowingUnitIndex) >= Match.Rule.ThrowsPerRound;

        public Round CurrentTeamRound(int roundNo) => CurrentUnit.Progress.Round(roundNo);

        public Round CurrentTeamLatestRound() => CurrentUnit.Progress.CurrentRound;
        public Round Round(string teamId, int roundNo)
        {
            return Match.ParticipantTeams.Unit(teamId).Progress.Round(roundNo);
        }

        public bool TryGetRound(string teamId, int roundNo, out Round round)
        {
            if(Match.ParticipantTeams.Unit(teamId).Progress.TryGetRound(roundNo, out var r))
            {
                round = r;
                return true;
            }
            round = null;
            return false;
        }

        public string MyTeamId(string granId)
        {
            try
            {
                return Match.ParticipantTeams.AllUnits.First(t => t.IsMember(granId)).Id;
            }
            catch
            {
                throw new NotMemberException($"{granId} not found in AllTeams.");
            }
        }

        /// <summary>
        /// GranEye displays a list of turn scores
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        public virtual List<int> GetScoreListOfRound(Round round)
        {
            List<int> result = new List<int>();
            if (round.TryGetRoundComponent<SegmentInput>(out var segment))
            {
                foreach (var th in segment.Throws)
                {
                    if (!th.IsEmpty)
                    {
                        result.Add(CalculateScore(th));
                    }
                }
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visit))
            {
                result.Add(visit.Score);
            }
            else
            {
                throw new InvalidRoundInput("This Round has no SegmentInput or VisitInput.");
            }
            return result;
        }

        protected virtual MatchFinishStatus CalculateRanking()
        {
            if (Participants.Count == 1) return new MatchFinishStatus(Participants.AllUnits[0]);

            //ゲームの順位決めのロジック
            //2チーム以上いる場合は全てのチームのスコアを高い順に並べ替え
            var ordered = Participants.AllUnits.OrderByDescending(team =>
            {
                //1.Score降順
                return CurrentScore(team.Id);
            }).ToList();
            //並べ替えたリストにgameRankingの値を1~チーム数上限(最大8)の値を降っていく
            int rank = 1;
            for (int i = 0; i < ordered.Count(); i++)
            {
                if (i == 0)
                {
                    Participants.Unit(ordered.ElementAt(i).Id).GameRanking = rank;
                    continue;
                }

                var previousTeamId = ordered.ElementAt(i - 1).Id;
                var currentTeamId = ordered.ElementAt(i).Id;

                if (CurrentScore(previousTeamId) == CurrentScore(currentTeamId))
                {
                    Participants.Unit(currentTeamId).GameRanking = rank;
                    continue;
                }

                Participants.Unit(currentTeamId).GameRanking = ++rank;
            }

            //もし全員のチームが同じ順位なら勝者は無しnullを返す
            //そうでない場合は勝者がいるので1位の人を返す
            var winner =
              IsDrawGame() ?
              Participants.AllUnits.First(team => team.GameRanking == 1) :
              null;


            return new MatchFinishStatus(winner);
        }
        //引き分けになったか判断する
        //teamRankingが1位のチームの個数が1以下なら
        //このゲームは引き分けではないという判断
        public bool IsDrawGame()
        {
            return Participants.AllUnits.GroupBy(team => team.GameRanking)
                   .Select(team => new { TeamRanking = team.Key, rankingCount = team.Count() })
                   .First(team => team.TeamRanking == 1).rankingCount <= 1;
        }

        public abstract Award[] AchievableAward
        {
            get;
        }

        public int TotalAchievedAwardCount(string granId, Award award)
        {
            Round[] result = null;
            foreach (var team in Participants.AllUnits)
            {
                if (team.IsMember(granId))
                {
                    var round = team.AllRoundsByMember(granId);
                    if (round != null)
                    {
                        result = round;
                    }
                }
            }

            if (result != null)
            {
                return result.Sum(r => GetAwardCount(r, award));
            }
            else
            {
                return 0;
            }
        }

        public int TotalAchievedAwardCountInRound(int roundIndex, string granId, Award award)
        {
            Round[] result = null;
            foreach (var team in Participants.AllUnits)
            {
                if (team.IsMember(granId))
                {
                    var round = team.AllRoundsByMember(granId);
                    if (round != null)
                    {
                        result = round;
                    }
                }
            }

            if (result != null && result.Length > 0)
            {
                if (result.Length > roundIndex)
                {
                    return GetAwardCount(result[roundIndex], award);
                }
                else
                {
                    return GetAwardCount(result.Last(), award);
                }

            }
            else
            {
                return 0;
            }
        }

        /// <summary>
        /// 因为回合有了Visit和Segment的区别所以奖励也要分开取
        /// </summary>
        /// <param name="round"></param>
        /// <param name="award"></param>
        /// <returns></returns>
        private int GetAwardCount(Round round, Award award)
        {
            var count = 0;
            var roundInput = round.GetRoundComponent<ARoundInput>();
            switch (roundInput)
            {
                case SegmentInput segmentInput:
                    count = AchievedAward(round).Contains(award) ? 1 : 0;
                    break;

                case VisitInput visitInput:
                    count = GranSteelVisitAchievedAward(visitInput.Score).Contains(award) ? 1 : 0;
                    break;
            }
            return count;
        }

        /// <summary>
        /// アワードが達成したかどうかを返します。
        /// これは基本のロジックです。
        /// ゲーム毎に達成する優先度があるものがあるため、その場合は実装クラスでオーバーライドしてください。
        ///
        /// Returns whether the award has been achieved.
        /// This is the basic logic.
        /// There is a priority to be achieved for each game, so in that case, override it in the implementation class.
        /// </summary>
        /// <param name="round"></param>
        /// <returns></returns>
        protected virtual Award[] AchievedAward(Round round)
        {
            List<Award> awards = new List<Award>();
            foreach (var award in AchievableAward)
            {
                switch (award)
                {
                    case Award.TonEighty:
                        if (AwardService.IsAchieveTon80(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.ThreeInTheBlack:
                        if (AwardService.IsAchieve3InTheBlack(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.ThreeInABed:
                        if (AwardService.IsAchieve3InABed(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.HatTrick:
                        if (AwardService.IsAchieveHatTrick(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.WhiteHorse:
                        if (AwardService.IsAchieveWhiteHorse(round))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.HighTon:
                        if (!AwardService.IsAchieveTon80(round) &&
                            !AwardService.IsAchieve3InTheBlack(round) &&
                            !AwardService.IsAchieveHatTrick(round) &&
                            AwardService.IsAchieveHighTon(TotalScore(round)))
                        {
                            awards.Add(award);
                        }

                        break;
                    case Award.LowTon:
                        if (!AwardService.IsAchieveTon80(round) &&
                            !AwardService.IsAchieve3InTheBlack(round) &&
                            !AwardService.IsAchieveHatTrick(round) &&
                            AwardService.IsAchieveLowTon(TotalScore(round)))
                        {
                            awards.Add(award);
                        }

                        break;
                    default:
                        throw new InvalidCodeException($"Invalid award. {award}");
                }
            }

            return awards.ToArray();
        }

        public virtual int TotalScore(Round round)
        {
            if (round.TryGetRoundComponent<SegmentInput>(out var segment))
            {
                return segment.Throws.Sum(t => CalculateScore(t));
            }
            else if (round.TryGetRoundComponent<VisitInput>(out var visitInput))
            {
                return visitInput.Score;
            }
            else
            {
                throw new InvalidRoundInput("This Round has no SegmentInput or VisitInput.");
            }
        }

        protected Award[] GranSteelVisitAchievedAward(int score)
        {
            List<Award> awards = new List<Award>();
            var totalScoreInRound = score;
            foreach (var award in AchievableAward)
            {
                switch (award)
                {
                    case Award.TonEighty:
                        if (totalScoreInRound == 180)
                        {
                            awards.Add(award);
                        }
                        break;
                    case Award.HighTon:
                        if (totalScoreInRound != 180 && AwardService.IsAchieveHighTon(totalScoreInRound))
                        {
                            awards.Add(award);
                        }
                        break;
                    case Award.LowTon:
                        if (AwardService.IsAchieveLowTon(totalScoreInRound))
                        {
                            awards.Add(award);
                        }
                        break;
                }
            }
            return awards.ToArray();
        }

        public List<Round> AllRoundsAtCurrentUnit => CurrentUnit.Progress.Rounds;

        public IEnumerable<Round> AllRoundsAt
        (
            string unitId
        )
        {
            var unit = Match.ParticipantTeams.Unit(unitId);
            return unit.Progress.Rounds;
        }

        public IEnumerable<Round> AllRoundsAtByPlayer
        (
            string granId
        )
        {
            return Match.ParticipantTeams.AllUnits.SelectMany(u => u.Progress.Rounds).Where(r => r.Thrower.GranId == granId);
        }

        public Round CurrentRoundAtCurrentTeam => CurrentUnit.Progress.CurrentRound;
        public int ThrowCountAtCurrentRoundAtCurrentTeam => CurrentUnit.Progress.CurrentThrowNo - 1;

        protected virtual void StartRoundAtCurrentTeam()
        {
            CurrentUnit.StartRound();
            EventPublisher.PublishRoundStart(Thrower);
        }

        protected void EndRoundAtCurrentTeam()
        {
            CurrentUnit.EndRound();
        }

        protected void FixRoundAtCurrentUnit()
        {
            CurrentUnit.FixRound();
        }

        public Participants Participants => Match.ParticipantTeams;

        public Player Thrower => CurrentUnit.CurrentThrower;

        public virtual void PreRevertCurrentRound() { }
        public virtual void PreRevertCurrentThrow() { }
        public virtual void PostRevertCurrentRound() { }

        public virtual void RevertCurrentRound()
        {
            if (CurrentUnitProgress.Rounds.Count == 1 && _currentThrowingUnitIndex == 0 &&
                CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
            {
                //1ラウンドの最初のチームが1投もしてない時にラウンドリバースした時は処理自体をしない
                //RoundのNoだと、Roundを継続できるゲームではうまく機能しない。Roundの数でみる。
                return;
            }

            try
            {
                EventPublisher.Enabled = false;
                var sender = CurrentUnit;
                var changedThrowingUnit = false;
                //もし現在のチームが1投もしてなければ
                //前のチームに戻す
                if (CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
                {
                    var discardsRoundNo = CurrentUnitProgress.CurrentRound.No;
                    //前のチームに戻るので現在のRoundを削除
                    CurrentUnitProgress.DiscardCurrentRound();

                    var previousRoundNo = CurrentUnitProgress.Rounds.Count > 0 ? CurrentUnitProgress.CurrentRound.No : 0;

                    if (previousRoundNo == discardsRoundNo)
                    {
                        //Rotationなどで、Roundを続行しているケースの場合、Unitのインデックスは戻さない。
                    }
                    else
                    {
                        //前のチームインデックスに戻す
                        //1引いた数値を入れ直す。最初のチームだった場合は最後のチームのインデックスにする。
                        _currentThrowingUnitIndex = (_currentThrowingUnitIndex - 1 < 0)
                            ? Match.ParticipantTeams.Count - 1
                            : _currentThrowingUnitIndex - 1;

                        changedThrowingUnit = true;
                    }
                }

                PreRevertCurrentRound();
                //現在のラウンドのデータをリセットして通知
                CurrentUnitProgress.ResetCurrentRound();
                ResetRoundData();
                RefreshGameData();

                PostRevertCurrentRound();

                EventPublisher.Enabled = true;
                EventPublisher.PublishRoundReverse(sender);
                if (changedThrowingUnit)
                {
                    EventPublisher.PublishChange(sender);
                }
            }
            finally
            {
                EventPublisher.Enabled = true;
            }
        }

        public virtual void RevertCurrentThrow()
        {
            if (CurrentUnitProgress.CurrentRound.No == 1 && _currentThrowingUnitIndex == 0 &&
                CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
            {
                //1ラウンドの最初のチームが1投もしてない時にラウンドリバースした時は処理自体をしない
                return;
            }

            var sender = CurrentUnit;
            //todo そもそもThrowリバースに前のラウンドに戻る処理がいるのか？
            //もし現在のチームが1投もしてなければ
            //前のチームに戻す
            if (CurrentUnitProgress.CurrentRound.ThrowCount <= 0)
            {
                var discardsRoundNo = CurrentUnitProgress.CurrentRound.No;
                //前のチームに戻るので現在のRoundを削除
                CurrentUnitProgress.DiscardCurrentRound();

                var previousRoundNo = CurrentUnitProgress.Rounds.Count > 0 ? CurrentUnitProgress.CurrentRound.No : 0;

                if (previousRoundNo == discardsRoundNo)
                {
                    //Rotationなどで、Roundを続行しているケースの場合、Unitのインデックスは戻さない。
                }
                else
                {
                    //前のチームインデックスに戻す
                    //1引いた数値を入れ直す。最初のチームだった場合は最後のチームのインデックスにする。
                    _currentThrowingUnitIndex = (_currentThrowingUnitIndex - 1 < 0)
                        ? Match.ParticipantTeams.Count - 1
                        : _currentThrowingUnitIndex - 1;
                    EventPublisher.PublishChange(sender);
                }
            }

            PreRevertCurrentThrow();
            //現在のラウンドの最新のスローを一つリセット
            CurrentUnitProgress.ResetCurrentRoundThrow();

            ResetThrowData();
            RefreshGameData();
            EventPublisher.PublishThrowReverse(sender);
        }

        public virtual void OverrideThrow(int throwNo, Segment segment)
        {
            if (CurrentUnitProgress.CurrentRound.InputType != RoundInputType.Segment)
            {
                throw new InvalidOperationException("This operation is only available for SegmentInput.");
            }
            if (CurrentRoundAtCurrentTeam.TryGetRoundComponent<OverwriteCounter>(out var overwriteCounter))
            {
                overwriteCounter.MarkOverwrite(throwNo);
            }
            EventPublisher.Enabled = false;
            var sender = CurrentUnit;
            try
            {
                //現在の投擲をコピーしておく。後で残りのThrowを復元するためです。
                Throw[] src = CurrentUnitProgress.CurrentRound.GetRoundComponent<SegmentInput>().Throws.ToArray();
                //修正したいThrowまで一旦戻す.
                while (CurrentUnitProgress.CurrentRound.ThrowCount >= throwNo)
                {
                    RevertCurrentThrow();
                }

                if (CurrentUnitProgress.CurrentRound.TryGetRoundComponent<IBustController>(out var bust))
                {
                    //BUSTの場合、Roundの最初から入れ直していく必要があります。VirtualHitの更新がされてしまっているためです。
                    CurrentUnitProgress.ResetCurrentRound();
                    if (throwNo >= 1)
                    {
                        foreach (var item in src.Take(throwNo - 1))
                        {
                            AcceptHit(item.ActuaryHitArea, item.HitPosition);
                        }
                    }
                }

                //新しい投擲を入れます。
                AcceptHit(segment, null);
                //残りのKeyはもとのままで復帰します。
                foreach (var item in src.Skip(throwNo))
                {
                    // 投げていないかBUSTで埋まったOUTが入っているのであれば復帰処理は終了します。
                    if (item.IsEmpty || item.ActuaryHitArea == Segment.OUT) break;
                    AcceptHit(item.ActuaryHitArea, item.HitPosition);
                }

                EventPublisher.Enabled = true;
                EventPublisher.PublishOverrideThrow(throwNo, sender);

                if (IsReachGameEnd)
                {
                    GameEnd();
                }
            }
            finally
            {
                EventPublisher.Enabled = true;
            }
        }
        protected abstract void ResetRoundData();

        protected virtual void ResetThrowData()
        {

        }

        public void Dispose()
        {
            EventPublisher.Dispose();
        }
    }
}
