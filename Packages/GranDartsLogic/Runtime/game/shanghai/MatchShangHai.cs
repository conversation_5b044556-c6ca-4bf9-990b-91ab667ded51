using System;
using System.Linq;
using System.Numerics;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.interfaces;

namespace com.luxza.grandartslogic.domain.game.shanghai
{
    public class MatchShangHai : IMatch<GameRuleShangHai>
    {
        private Participants _participants { get; }
        
        internal MatchShangHai(Unit[] participants, GameRuleShangHai rule)
        {
            _participants = new Participants(participants);
            Rule = rule;
            for (int i = 0; i < participants.Length; i++)
            {
                ParticipantTeams.Unit(i).InitializeProgress(rule.ThrowsPerRound, GameCode);
            }
        }
        
        public RefereeShangHai CreateReferee()
        {
            return new RefereeShangHai(this);
        }

        public int TeamThrowCountOnLatestRound(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress?.CurrentRound.ThrowCount ?? 0;
        }

        public Throw LatestThrow(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress.LatestThrow;
        }

        public Participants ParticipantTeams => _participants;
        public GameCode GameCode => Rule.Code.Value;
        public GameRuleShangHai Rule { get; }

        public bool IsReachAllTeamsToMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team =>
                {
                    return team.Progress.CurrentRoundNo >= Rule.MaxRound &&
                           team.Progress.CurrentRound.GetRoundComponent<IRoundInput>().IsAllThrowsFixed();
                });
            }
        }
        
        public bool IsNextRoundToOverMaxRound
        {
            get
            {
                return ParticipantTeams.AllUnits.All(team =>
                {
                    return team.Progress.CurrentRoundNo >= Rule.MaxRound;
                });
            }
        }

        public bool IsReachEndOfRound(int teamIndex)
        {
            return ParticipantTeams.Unit(teamIndex).Progress.CurrentRound.ThrowCount >= 3;
        }

        public void StoreHit(int teamIndex, Segment actuaryHit, Segment virtualHit, Vector2? hitPosition = null)
        {
            var progress = ParticipantTeams.Unit(teamIndex).Progress;
            progress.FixThrow(actuaryHit, virtualHit, hitPosition);
        }

        public override string ToString()
        {
            return $"Shanghai Match: {ParticipantTeams.Count} teams, Rule: {Rule.MaxRound} rounds";
        }
    }
}