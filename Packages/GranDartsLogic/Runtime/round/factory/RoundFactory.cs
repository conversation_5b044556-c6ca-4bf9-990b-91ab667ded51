using System;
using com.luxza.grandartslogic.domain.game.baseball;
using com.luxza.grandartslogic.domain.game.beyondtop;
using com.luxza.grandartslogic.domain.game.countup;
using com.luxza.grandartslogic.domain.game.crcountup;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.cutthroat;
using com.luxza.grandartslogic.domain.game.deltashoot;
using com.luxza.grandartslogic.domain.game.halfit;
using com.luxza.grandartslogic.domain.game.hyperbull;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.Oniren;
using com.luxza.grandartslogic.domain.game.pirates;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.round.life;
using com.luxza.grandartslogic.domain.game.shootforce;
using com.luxza.grandartslogic.domain.game.spider;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.targetbull;
using com.luxza.grandartslogic.domain.game.targetchoice;
using com.luxza.grandartslogic.domain.game.targethat;
using com.luxza.grandartslogic.domain.game.targethorse;
using com.luxza.grandartslogic.domain.game.teamcr;
using com.luxza.grandartslogic.domain.game.WildCardcricket;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.game.round.components.analyzer;

namespace com.luxza.grandartslogic.domain.game.round.factory
{
    public static class RoundFactory
    {
        public static Round CreateRound
        (
            int no,
            Player thrower,
            GameCode game
        )
        {
            switch (game)
            {
                case GameCode._301:
                case GameCode._501:
                case GameCode._701:
                case GameCode._901:
                case GameCode._1101:
                case GameCode._1501:
                case GameCode._ANIMAL_301GAME:
                case GameCode._ANIMAL_501GAME:
                case GameCode._ANIMAL_701GAME:
                case GameCode._ANIMAL_901GAME:
                case GameCode._ANIMAL_1101GAME:
                case GameCode._ANIMAL_1501GAME:
                    return Create01Round(no, thrower);
                case GameCode._StandardCR:
                case GameCode._HiddenCR:
                case GameCode._ANIMAL_STANDARD_CR:
                    return CreateBasicRound(no, thrower, GameRuleStandardCR.AchievableAward);
                case GameCode._CutThroat:
                case GameCode._HiddenCutThroat: return CreateBasicRound(no, thrower, GameRuleCutThroat.AchievableAward);
                case GameCode._Countup: return CreateBasicRound(no, thrower, GameRuleCountUp.AchievableAward);
                case GameCode._CrCountUp: return CreateBasicRound(no, thrower, GameRuleCRCountUp.AchievableAward);
                case GameCode._HalfIt: return CreateBasicRound(no, thrower, GameRuleHalfIt.AchievableAward);
                case GameCode._DeltaShoot: return CreateBasicRound(no, thrower, GameRuleDeltaShoot.AchievableAward);
                case GameCode._ShootForce: return CreateBasicRound(no, thrower, GameRuleShootForce.AchievableAwards);
                case GameCode._Oniren:
                    return CreateBasicRound(no, thrower, GameRuleOniren.AchievableAward).
                        AddRoundComponent(new OnirenLife());
                case GameCode._TargetBull: return CreateBasicRound(no, thrower, GameRuleTargetBull.AchievableAward);
                case GameCode._Target20: return CreateBasicRound(no, thrower, GameRuleTarget20.AchievableAward);
                case GameCode._TargetHat: return CreateBasicRound(no, thrower, GameRuleTargetHat.AchievableAward);
                case GameCode._TargetChoice: return CreateBasicRound(no, thrower, GameRuleTargetChoice.AchievableAward);
                case GameCode._TargetHorse: return CreateBasicRound(no, thrower, GameRuleTargetHorse.AchievableAward);
                case GameCode._MultipleCr: return CreateBasicRound(no, thrower, GameRuleMultipleCR.AchievableAward);
                case GameCode._Pirates: return CreateBasicRound(no, thrower, GameRulePirates.AchievableAward);
                case GameCode._Spider: return CreateBasicRound(no, thrower, GameRuleSpider.AchievableAwards);
                case GameCode._Rotation: return CreateRotaionRound(no, thrower);
                case GameCode._BeyondTop: return CreateBeyondTopRound(no, thrower);
                //TODO:Award
                case GameCode._HideAndSeek: return CreateBasicRound(no, thrower, Array.Empty<Award>());
                case GameCode._HyperBull: return CreateHyperBullRound(no, thrower);
                //TODO:Award
                case GameCode._2Line:
                //TODO:Award
                case GameCode._FunMission:
                //TODO:Award
                case GameCode._TreasureHunt:
                //TODO:Award
                case GameCode._TicTacToe: return CreateBasicRound(no, thrower, Array.Empty<Award>());
                case GameCode._KickDown301:
                case GameCode._KickDown501: return CreateKickDownRound(no, thrower);
                case GameCode._Freeze301:
                case GameCode._Freeze501:
                case GameCode._Freeze701:
                case GameCode._Freeze901:
                case GameCode._Freeze1101:
                case GameCode._Freeze1501: return Create01Round(no, thrower);
                case GameCode._TeamCR: return CreateBasicRound(no, thrower, GameRuleTeamCR.AchievableAward);
                case GameCode._WildCardCR: return CreateBasicRound(no, thrower, GameRuleWildCardCR.AchievableAward);
                case GameCode._BaseBall: return CreateBasicRound(no, thrower, GameRuleBaseBall.AchievableAwards);
                default: throw new ArgumentOutOfRangeException(nameof(game), game, null);
            }
        }


        private static Round Create01Round
        (
            int no,
            Player thrower
        )
        {
            var round = new Round(no, thrower);

            if (thrower.RoundInputType == RoundInputType.Visit)
            {
                round.AddRoundComponent(new VisitInput(1));
            }
            else
            {
                round.AddRoundComponent(new SegmentInput(3));
                if (thrower.InputDevice == InputDevice.GranEye)
                {
                    round.AddRoundComponent(new OverwriteCounter());
                }
            }

            return round.AddRoundComponent
                    (new ZeroOneBustController()).
                AddRoundComponent(new AwardChanceDetector(GameRule01.AchievableAwards));
        }

        private static Round CreateRotaionRound
        (
            int no,
            Player thrower
        )
        {
            var round = new Round(no, thrower);

            if (thrower.RoundInputType == RoundInputType.Visit)
            {
                round.AddRoundComponent(new VisitInput(1));
            }
            else
            {
                round.AddRoundComponent(new SegmentInput(3));
                if (thrower.InputDevice == InputDevice.GranEye)
                {
                    round.AddRoundComponent(new OverwriteCounter());
                }
            }

            return round.AddRoundComponent(new AwardChanceDetector(GameRuleRotation.AchievableAward));
        }

        private static Round CreateHyperBullRound
        (
            int no,
            Player thrower
        )
        {
            var round = new Round(no, thrower).AddRoundComponent
                    (new SegmentInput(3)).
                AddRoundComponent(new ZeroOneBustController()).
                AddRoundComponent(new AwardChanceDetector(GameRuleHyperBull.AchievableAward));
            if (thrower.InputDevice == InputDevice.GranEye)
            {
                round.AddRoundComponent(new OverwriteCounter());
            }

            return round;
        }

        private static Round CreateKickDownRound
        (
            int no,
            Player thrower
        )
        {
            var round = new Round(no, thrower);

            if (thrower.RoundInputType == RoundInputType.Visit)
            {
                throw new NotSupportedException("KickDown does not support Visit input.");
            }
            else
            {
                round.AddRoundComponent(new SegmentInput(3));
                if (thrower.InputDevice == InputDevice.GranEye)
                {
                    round.AddRoundComponent(new OverwriteCounter());
                }
            }

            return round.AddRoundComponent
                    (new KickDownOverScoreController()).
                AddRoundComponent(new KickdownAdditionalRound()).
                AddRoundComponent(new AwardChanceDetector(GameRuleKickDown.AchievableAward));
        }

        private static Round CreateBeyondTopRound
        (
            int no,
            Player thrower
        )
        {
            var round = new Round(no, thrower);

            if (thrower.RoundInputType == RoundInputType.Visit)
            {
                round.AddRoundComponent(new VisitInput(1));
            }
            else
            {
                round.AddRoundComponent(new SegmentInput(3));
                if (thrower.InputDevice == InputDevice.GranEye)
                {
                    round.AddRoundComponent(new OverwriteCounter());
                }
            }
            round.AddRoundComponent(new BeyondTopGoal());
            return round.AddRoundComponent(new AwardChanceDetector(GameRuleBeyondTop.AchievableAward));
        }

        private static Round CreateBasicRound
        (
            int no,
            Player thrower,
            Award[] achieveAwards,
            int throwCountPerRound = 3
        )
        {
            var round = new Round(no, thrower);

            if (thrower.RoundInputType == RoundInputType.Visit)
            {
                round.AddRoundComponent(new VisitInput(1));
            }
            else
            {
                round.AddRoundComponent(new SegmentInput(throwCountPerRound));
                if (thrower.InputDevice == InputDevice.GranEye)
                {
                    round.AddRoundComponent(new OverwriteCounter());
                }
            }
            return round.AddRoundComponent(new AwardChanceDetector(achieveAwards));
        }
    }
}