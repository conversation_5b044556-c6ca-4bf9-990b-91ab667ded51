using System.Linq;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.baseball;
using com.luxza.grandartslogic.domain.game.beyondtop;
using com.luxza.grandartslogic.domain.game.countup;
using com.luxza.grandartslogic.domain.game.crcountup;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.cutthroat;
using com.luxza.grandartslogic.domain.game.deltashoot;
using com.luxza.grandartslogic.domain.game.Freeze_zeroone;
using com.luxza.grandartslogic.domain.game.funmission;
using com.luxza.grandartslogic.domain.game.halfit;
using com.luxza.grandartslogic.domain.game.hideandseek;
using com.luxza.grandartslogic.domain.game.hyperbull;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.Oniren;
using com.luxza.grandartslogic.domain.game.pirates;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.shootforce;
using com.luxza.grandartslogic.domain.game.spider;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.targetbull;
using com.luxza.grandartslogic.domain.game.targetchoice;
using com.luxza.grandartslogic.domain.game.targethat;
using com.luxza.grandartslogic.domain.game.targethorse;
using com.luxza.grandartslogic.domain.game.teamcr;
using com.luxza.grandartslogic.domain.game.tictactoe;
using com.luxza.grandartslogic.domain.game.treasurehunt;
using com.luxza.grandartslogic.domain.game.twolinebattle;
using com.luxza.grandartslogic.domain.game.WildCardcricket;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.domain.settings;

namespace com.luxza.grandartslogic.domain
{
    public static class MatchMaker
    {
        public static Referee01 Make01
        (
            Unit[] participants,
            GameRule01 rule,
            bool isMedley,
            string arrangeListText
        )
        {
            var match = new Match01(participants, rule, isMedley, arrangeListText);

            if (rule.HandicapSetting == HandicapSetting.Auto)
            {
                double highestUnitRating = participants.FindUnitByHighestRating01().RatingZeroOne;

                foreach (var t in participants)
                {
                    t.SetHandicapScore(match.AutoHandicapScore(t, (highestUnitRating - t.RatingZeroOne)), new int[1]);
                }
            }
            return match.CreateReferee(rule.Mode == ZeroOneGameMode.Continuous);
        }

        public static RefereeFreeze01 MakeFreeze01
        (
            Unit[] participants,
            GameRuleFreeze01 rule,
            string arrangeListText
        )
        {
            var match = new MatchFreeze01(participants, rule, arrangeListText);

            return match.CreateReferee();
        }

        public static RefereeKickDown MakeKickDown
        (
            Unit[] participants,
            GameRuleKickDown rule,
            string arrangeListText
        )
        {
            var match = new MatchKickDown(participants, rule, arrangeListText);

            return match.CreateReferee();
        }


        public static RefereeStandardCR MakeStandardCr
        (
            Unit[] participants,
            GameRuleStandardCR rule,
            bool isMedley
        )
        {
            var match = new MatchStandardCR(participants, rule, isMedley);

            if (rule.Handicap == HandicapSetting.Auto)
            {
                double highestteamRating = participants.FindUnitByHighestRatingCRTeam().RatingCR;
                return match.CreateReferee
                (
                    participants.Select
                    (
                        t =>
                            match.CreateAutoHandicap(t, (highestteamRating - t.RatingCR))
                    )
                );
            }
            else if (rule.Handicap == HandicapSetting.Auto)
            {
                return match.CreateReferee
                (
                    participants.Select
                    (
                        t =>
                            match.SetManuelHandicap(t.Id, t.HandicapScore, t.HandicapMarks)
                    )
                );
            }

            return match.CreateReferee(participants.Select(t => match.NoneHandicap(t.Id)));
        }

        public static RefereeCutThroat MakeCutThroat
        (
            Unit[] participants,
            GameRuleCutThroat rule
        )
        {
            var match = new MatchCutThroat(participants, rule);

            return match.CreateReferee();
        }

        public static RefereeTeamCR MakeTeamCr
        (
            Unit[] participants,
            GameRuleTeamCR rule
        )
        {
            var match = new MatchTeamCR(participants, rule);

            return match.CreateReferee();
        }

        public static RefereeWildCardCR MakeWildCardCr
        (
            Unit[] participants,
            GameRuleWildCardCR rule
        )
        {
            var match = new MatchWildCardCR(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeCRCountUp MakeCRCountUp
        (
            Unit[] participants,
            GameRuleCRCountUp rule
        )
        {
            var match = new MatchCRCountUp(participants, rule);

            return match.CreateReferee();
        }

        public static RefereeCountUp MakeCountUp
        (
            Unit[] participants,
            GameRuleCountUp rule
        )
        {
            var match = new MatchCountUp(participants, rule);
            return match.CreateReferee();
        }


        public static RefereeHyperBull MakeHyperBull
        (
            Unit[] participants,
            GameRuleHyperBull rule
        )
        {
            var match = new MatchHyperBull(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeHalfIt MakeHalfIt
        (
            Unit[] participants,
            GameRuleHalfIt rule
        )
        {
            var match = new MatchHalfIt(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeMultipleCR MakeMultipleCR
        (
            Unit[] participants,
            GameRuleMultipleCR rule,
            bool testrandomarrange = false
        )
        {
            var match = new MatchMultipleCR(participants, rule, testrandomtarget: testrandomarrange);
            return match.CreateReferee();
        }

        public static RefereeSpider MakeSpider
        (
            Unit[] participants,
            GameRuleSpider rule,
            int testArrange = 0
        )
        {
            var match = new MatchSpider(participants, rule);
            return match.CreateReferee(testArrange);
        }

        public static RefereePirates MakePirates
        (
            Unit[] participants,
            GameRulePirates rule
        )
        {
            var match = new MatchPirates(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeOniren MakeOniren
        (
            Unit[] participants,
            GameRuleOniren rule
        )
        {
            var match = new MatchOniren(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeDeltaShoot MakeDeltaShoot
        (
            Unit[] participants,
            GameRuleDeltaShoot rule
        )
        {
            var match = new MatchDeltaShoot(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeRotation MakeRotation
        (
            Unit[] participants,
            GameRuleRotation rule
        )
        {
            var match = new MatchRotation(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTargetBull MakeTargetBull
        (
            Unit[] participants,
            GameRuleTargetBull rule
        )
        {
            var match = new MatchTargetBull(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTarget20 MakeTarget20
        (
            Unit[] participants,
            GameRuleTarget20 rule
        )
        {
            var match = new MatchTarget20(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTargetChoice MakeTargetChoice
        (
            Unit[] participants,
            GameRuleTargetChoice rule
        )
        {
            var match = new MatchTargetChoice(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTargetHat MakeTargetHat
        (
            Unit[] participants,
            GameRuleTargetHat rule
        )
        {
            var match = new MatchTargetHat(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTargetHorse MakeTargetHorse
        (
            Unit[] participants,
            GameRuleTargetHorse rule
        )
        {
            var match = new MatchTargetHorse(participants, rule);
            return match.CreateReferee();
        }


        public static RefereeBeyondTop MakeBeyondTop
        (
            Unit[] participants,
            GameRuleBeyondTop rule
        )
        {
            var match = new MatchBeyondTop(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeShootForce MakeShootForce
        (
            Unit[] participants,
            GameRuleShootForce rule
        )
        {
            var match = new MatchShootForce(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeFunMission MakeFunMission
        (
            Unit[] participants,
            GameRuleFunMission rule
        )
        {
            var match = new MatchFunMission(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTwoLineBattle MakeTwoLineBattle
        (
            Unit[] participants,
            GameRuleTwoLineBattle rule
        )
        {
            var match = new MatchTwoLineBattle(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTreasureHunt MakeTreasureHunt
        (
            Unit[] participants,
            GameRuleTreasureHunt rule
        )
        {
            var match = new MatchTreasureHunt(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeHideAndSeek MakeHideAndSeek
        (
            Unit[] participants,
            GameRuleHideAndSeek rule
        )
        {
            var match = new MatchHideAndSeek(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeTicTacToe MakeTicTacToe
        (
            Unit[] participants,
            GameRuleTicTacToe rule
        )
        {
            var match = new MatchTicTacToe(participants, rule);
            return match.CreateReferee();
        }

        public static RefereeBaseBall MakeBaseBall
        (
            Unit[] participants,
            GameRuleBaseBall rule
        )
        {
            var match = new MatchBaseBall(participants, rule);
            return match.CreateReferee();
        }
    }
}
