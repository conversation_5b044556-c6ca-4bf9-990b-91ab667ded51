using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.graneye;
using com.luxza.graneye.exception;
using com.luxza.granlog;
using com.luxza.ui.components.molecules;
using com.luxza.ui.components.organizms;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;
using UnityEngine.UI;

public class PageCalibrationCheckPointView : MonoBehaviour
{
    [SerializeField] private Page _page;
    [SerializeField] private GranHeader _granHeader;
    [SerializeField] private RawImage _rimg_Board;
    [SerializeField] private GameObject[] _pointList;
    [SerializeField] private GranButton _btn_Redetection;
    [SerializeField] private GranButton _btn_ManualSet;
    [SerializeField] private GranButton _btn_Next;
    [SerializeField] private RectTransform _rectTransform_Dot;
    [SerializeField] private Button _btn_ReloadCameraTex;
    [SerializeField] private GameObject _gameObject_Loading;
    
    private GranEyeSettingParameter _parameter;
    private int _cameraTryGetTexCount = 0;
    
    void Awake()
    {
        _page.OnActivate.Subscribe(async _ => await OnActivate()).RegisterTo(destroyCancellationToken);
        _granHeader.onClickLeftIcon1.Subscribe(async _ => await OnBackClicked()).RegisterTo(destroyCancellationToken);
        _btn_Redetection.onClickAsObservable.Subscribe(async _ => await OnRedetectionClicked()).RegisterTo(destroyCancellationToken);
        _btn_ManualSet.onClickAsObservable.Subscribe(async _ => await OnManualSetClicked()).RegisterTo(destroyCancellationToken);
        _btn_Next.onClickAsObservable.Subscribe(async _ => await OnNextClicked()).RegisterTo(destroyCancellationToken);
        
        _btn_ReloadCameraTex.onClick.AddListener(async () =>
        {
            _btn_ReloadCameraTex.gameObject.SetActive(false);
            await OnRedetectionClicked();
        });
    }
    
    private async UniTask OnActivate()
    {
        if(!_page.Meta.TryGetParameter<GranEyeSettingParameter>(out var parameter)) {
            throw new ArgumentException("GranEyeSettingParameter is required.");
        }
        _parameter = parameter;
        Log.d($"_parameter.CameraPos = {_parameter.CameraPos}");
        for (int i = 0; i < 2; i++)
        {
            _pointList[i].SetActive(i == (int)_parameter.CameraPos);
        }
        if (_parameter.Texture != null)
        {
            _rimg_Board.texture = _parameter.Texture;
            _rimg_Board.color = Color.white;
        }
        else
        {
            var cameraRespondData = await GranEye.Instance.GetCameraViewData(_parameter.CameraPos);
            if (cameraRespondData.Result)
            {
                _parameter.Texture = _rimg_Board.texture = cameraRespondData.CameraTexture;
                _rimg_Board.color = Color.white;
            }
        }
        if (_rimg_Board.texture != null)
        {
            _gameObject_Loading.SetActive(true);
            var isOk = await GranEye.Instance.AutomaticCalibration(_parameter.CameraPos, _rimg_Board.texture);
            _gameObject_Loading.SetActive(false);
            if (!isOk)
            {
                if (GranEyeSettingSaver.IsGuide)
                {
                    this.ShowFailedTip();
                }
                else
                {
                    AddOldDots(_parameter.CameraPos);
                }
            }
        }
        else
        {
            _btn_ReloadCameraTex.gameObject.SetActive(true);
        }
    }

    private async UniTask OnBackClicked()
    {
        await PageManager.Instance.BackUntil(new[]
            { PageNames.GranEyeCalibrationDescription, PageNames.GranEyeBoardSetting });
    }

    private async UniTask OnRedetectionClicked()
    {
        _btn_Redetection.loading = true;
        _gameObject_Loading.SetActive(true);
        foreach (Transform child in _rimg_Board.transform)
        {
            Destroy(child.gameObject);
        }
        _rimg_Board.texture  = null;
        _rimg_Board.color = new Color(70 / 255f, 70 / 255f, 70 / 255f, 255 / 255f);
        await TakePicture(_parameter.CameraPos);
        if (_rimg_Board.texture != null)
        {
            var isOk = await GranEye.Instance.AutomaticCalibration(_parameter.CameraPos, _rimg_Board.texture);
            _btn_Redetection.loading = false;
            _gameObject_Loading.SetActive(false);
            if (!isOk)
            {
                if (GranEyeSettingSaver.IsGuide)
                {
                    this.ShowFailedTip();
                }
                else
                {
                    AddOldDots(_parameter.CameraPos);
                }
            }
        }
        else
        {
            _btn_Redetection.loading = false;
            _gameObject_Loading.SetActive(false);
            _btn_ReloadCameraTex.gameObject.SetActive(true);
        }
    }
    
    private async UniTask OnManualSetClicked()
    {
        GranEye.Instance.DeleteAllDots(_parameter.CameraPos);
        if (GranEyeSettingSaver.IsGuide)
        {
            _parameter.IsHaveNextCamera = _parameter.CameraPos == GranEyeCore.CameraPos.Left;
        }
        await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeManualSet, _parameter));
    }
    
    private async UniTask OnNextClicked()
    {
        _parameter.Texture = _rimg_Board.texture;
        if (GranEyeSettingSaver.IsGuide)
        {
            _parameter.IsHaveNextCamera = _parameter.CameraPos == GranEyeCore.CameraPos.Left;
        }
        await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyePointCheck, _parameter));
    }
    
    private async UniTask TakePicture(GranEyeCore.CameraPos cameraPos, bool addFailCount = true)
    {
        if (_rimg_Board.texture == null)
        {
            var granEyeEntity = await GranEye.Instance.GetCameraViewData(cameraPos);
            if (granEyeEntity.Result)
            {
                _btn_ReloadCameraTex.gameObject.SetActive(false);
                _parameter.Texture = _rimg_Board.texture = granEyeEntity.CameraTexture;
                _rimg_Board.color = Color.white;
            }
            else
            {
                _btn_ReloadCameraTex.gameObject.SetActive(true);
                if (!addFailCount)
                {
                    return;
                }
                _cameraTryGetTexCount++;
                if (_cameraTryGetTexCount == GranEye.MaxGetTexCount)
                {
                    _cameraTryGetTexCount = 0;
                    MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Tips.RestartCamera),
                        iconType: GranModal.IconType.OK,
                        positive: (
                            LocalizeString.GetLocalizedString(LocalizeKey.Labels.OK), async () =>
                            {
                                var result = await GranEye.Instance.Restart(cameraPos);
                                if(result)
                                {
                                    MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Notifications.CameraRestartCheck), 
                                        iconType: GranModal.IconType.OK,
                                        positive: (
                                            LocalizeString.GetLocalizedString(LocalizeKey.Labels.OK), async () =>
                                            {
                                                await TakePicture(cameraPos, false);
                                            }));
                                }
                            }),
                        negative: (LocalizeString.GetLocalizedString(LocalizeKey.Labels.Cancel), async () => { }));
                }
            }
        }
    }
    
    // private async UniTask<bool> AutomaticCalibration(GranEyeCore.CameraPos cameraPos)
    // {
    //     var culture = CultureInfo.InvariantCulture;
    //     var cancellationPosList = new Dictionary<int, Vector3>();
    //     try
    //     {
    //         Log.d($"{cameraPos}, Start auto calibration");
    //         var posInfoList = new Dictionary<int, TFEntities.PredictResultPointEntity>();
    //
    //         List<TFEntities.PredictResultPointEntity> tPoints;
    //         tPoints = await GranEye.GranEyeCore.PredictBoard(_rimg_Board.texture);
    //
    //         if (tPoints == null || !tPoints.Any())
    //         {
    //             Log.w("tPoints is empty or null");
    //             return false;
    //         }
    //
    //         for (var i = 0; i < tPoints.Count; i++)
    //         {
    //             if (posInfoList.ContainsKey(tPoints[i].Tag))
    //             {
    //                 if (posInfoList[tPoints[i].Tag].score < tPoints[i].score)
    //                     posInfoList[tPoints[i].Tag] = tPoints[i];
    //             }
    //             else
    //             {
    //                 posInfoList.Add(tPoints[i].Tag, tPoints[i]);
    //             }
    //         }
    //         
    //         posInfoList = posInfoList.OrderBy(x => x.Key).ToDictionary(x => x.Key, x => x.Value);
    //         
    //         var tipStr = string.Empty;
    //         
    //         foreach (var pointInfo in posInfoList)
    //         {
    //             var pos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(pointInfo.Value.x, pointInfo.Value.y));
    //             Log.d($"{pointInfo.Key},x = {pos.x},y = {pos.y}");
    //             tipStr += $"tag{pointInfo.Key}, x:{pointInfo.Value.x:0.0}, y:{pointInfo.Value.y:0.0}, score:{pointInfo.Value.score * 100:0.0}%\n";
    //             
    //             GranEye.GranEyeCore.Set_Dot(cameraPos, CalibrationType.Auto, pointInfo.Key - 1, pos);
    //             cancellationPosList.Add(pointInfo.Key, pos);
    //         }
    //
    //         if (cancellationPosList.Count == 4)
    //         {
    //             var isOK = GranEye.GranEyeCore.Calibration(cameraPos, cancellationPosList.Values.ToList());
    //             if (isOK)
    //             {
    //                 foreach (Transform child in _rimg_Board.transform)
    //                 {
    //                     Destroy(child.gameObject);
    //                 }
    //                 try
    //                 {
    //                     AddOldDots(cameraPos);
    //                 }
    //                 catch (GranEyeHomoCoreIsNotInitException)
    //                 {
    //                     MessageUtility.ShowMessageModal(LocalizeString.GetLocalizedString(LocalizeKey.Systems.GranEyeHomoCoreIsNotInitError));
    //                 }
    //             }
    //             return isOK;
    //         }
    //         else
    //         {
    //             Log.e($"Calibration failed, only {cancellationPosList.Count} points detected.\n{tipStr}");
    //             return false;
    //         }
    //     }
    //     catch (Exception ex)
    //     {
    //         Log.w($"An error occurred: {ex.Message}\n{ex.StackTrace}");
    //         return false;
    //     }
    // }

    private async void ShowFailedTip()
    {
        if (this == null)
        {
            return;
        }
        await PageManager.Instance.OpenAsModalAsync(new PageMeta(PageNames.GranEyeCalibrationPointErrorModal,
            new CalibrationPointErrorParameter(async result =>
            {
                if (result.Item1)
                {
                    await OnRedetectionClicked();
                }
                else if (result.Item2)
                {
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GranEyeManualCalibrationCheckPoint,
                        _parameter));
                }
            })));
    }

    private void AddOldDots(GranEyeCore.CameraPos cameraPos)
    {
        var granEyeEntity = GranEye.Instance.Get_DotList(cameraPos);
        for (int i = 0; i < 4; i++)
        {
            var pos = GetDotLocalPositionFromNormalized(granEyeEntity.DotList[i], _rimg_Board.rectTransform);
            var rectTransform = Instantiate(_rectTransform_Dot, _rimg_Board.GetComponent<RectTransform>());
            rectTransform.localPosition = pos;
            rectTransform.gameObject.SetActive(true);
            rectTransform.sizeDelta = new Vector2(10, 10);
            rectTransform.GetComponent<Image>().color = Color.yellow;
        }
    }

    private Vector2 GetDotLocalPositionFromNormalized(Vector2 normalizedPos, RectTransform rawImageRect)
    {
        Vector2 size = rawImageRect.rect.size;

        return new Vector2(
            normalizedPos.x * size.x,
            normalizedPos.y * size.x
        );
    }
}
