{"TestSuite": "", "Date": 0, "Player": {"Development": false, "ScreenWidth": 0, "ScreenHeight": 0, "ScreenRefreshRate": 0, "Fullscreen": false, "Vsync": 0, "AntiAliasing": 0, "Batchmode": false, "RenderThreadingMode": "MultiThreaded", "GpuSkinning": false, "Platform": "", "ColorSpace": "", "AnisotropicFiltering": "", "BlendWeights": "", "GraphicsApi": "", "ScriptingBackend": "IL2CPP", "AndroidTargetSdkVersion": "AndroidApiLevel34", "AndroidBuildSystem": "<PERSON><PERSON><PERSON>", "BuildTarget": "Android", "StereoRenderingPath": "MultiPass"}, "Hardware": {"OperatingSystem": "", "DeviceModel": "", "DeviceName": "", "ProcessorType": "", "ProcessorCount": 0, "GraphicsDeviceName": "", "SystemMemorySizeMB": 0}, "Editor": {"Version": "6000.0.27f1", "Branch": "6000.0/staging", "Changeset": "27c554a2199c", "Date": 1730906052}, "Dependencies": ["com.annulusgames.lit-motion@2.0.1", "com.annulusgames.lit-motion.animation@2.0.1", "com.cysharp.r3@1.2.9", "com.cysharp.unitask@2.5.10", "com.cysharp.yetanotherhttphandler@1.5.2", "com.github-glitchenzo.nugetforunity@4.1.1", "com.github.asus4.mediapipe@2.17.0", "com.google.external-dependency-manager@1.2.183", "com.google.firebase.analytics@12.5.0", "com.google.firebase.app@12.5.0", "com.google.firebase.auth@12.5.0", "com.google.firebase.crashlytics@12.5.0", "com.google.firebase.firestore@12.5.0", "com.luxza.gran_online_match_server_client@1.1.5", "com.luxza.grancamera2@1.0.52", "com.luxza.granecho@1.0.11", "com.luxza.graneyesdk@0.1.0", "com.luxza.granunitylog@1.2.7", "com.luxza.uiframework@0.1.58", "com.luxza.videochat@0.1.0", "com.unity.collab-proxy@2.5.2", "com.unity.device-simulator.devices@1.0.0", "com.unity.feature.2d@2.0.1", "com.unity.ide.rider@3.0.34", "com.unity.ide.visualstudio@2.0.22", "com.unity.inputsystem@1.11.2", "com.unity.localization@1.5.3", "com.unity.memoryprofiler@1.1.6", "com.unity.mobile.android-logcat@1.4.3", "com.unity.multiplayer.center@1.0.0", "com.unity.render-pipelines.universal@17.0.3", "com.unity.test-framework@1.4.5", "com.unity.testtools.codecoverage@1.2.6", "com.unity.timeline@1.8.7", "com.unity.ugui@2.0.0", "com.unity.visualscripting@1.9.4", "com.yasirkula.imagecropper@1.0.7", "com.yasirkula.nativegallery@1.8.1", "net.gree.unity-webview@1.0.0", "com.unity.modules.accessibility@1.0.0", "com.unity.modules.ai@1.0.0", "com.unity.modules.androidjni@1.0.0", "com.unity.modules.animation@1.0.0", "com.unity.modules.assetbundle@1.0.0", "com.unity.modules.audio@1.0.0", "com.unity.modules.cloth@1.0.0", "com.unity.modules.director@1.0.0", "com.unity.modules.imageconversion@1.0.0", "com.unity.modules.imgui@1.0.0", "com.unity.modules.jsonserialize@1.0.0", "com.unity.modules.particlesystem@1.0.0", "com.unity.modules.physics@1.0.0", "com.unity.modules.physics2d@1.0.0", "com.unity.modules.screencapture@1.0.0", "com.unity.modules.terrain@1.0.0", "com.unity.modules.terrainphysics@1.0.0", "com.unity.modules.tilemap@1.0.0", "com.unity.modules.ui@1.0.0", "com.unity.modules.uielements@1.0.0", "com.unity.modules.umbra@1.0.0", "com.unity.modules.unityanalytics@1.0.0", "com.unity.modules.unitywebrequest@1.0.0", "com.unity.modules.unitywebrequestassetbundle@1.0.0", "com.unity.modules.unitywebrequestaudio@1.0.0", "com.unity.modules.unitywebrequesttexture@1.0.0", "com.unity.modules.unitywebrequestwww@1.0.0", "com.unity.modules.vehicles@1.0.0", "com.unity.modules.video@1.0.0", "com.unity.modules.vr@1.0.0", "com.unity.modules.wind@1.0.0", "com.unity.modules.xr@1.0.0", "com.luxza.dartslogic@1.0.0", "com.luxza.unitygraphqlclient@1.9.0", "com.unity.addressables@2.2.2", "com.unity.nuget.newtonsoft-json@3.2.1", "com.unity.modules.subsystems@1.0.0", "com.unity.modules.hierarchycore@1.0.0", "com.unity.settings-manager@2.0.1", "com.unity.ext.nunit@2.0.5", "com.unity.render-pipelines.core@17.0.3", "com.unity.shadergraph@17.0.3", "com.unity.render-pipelines.universal-config@17.0.3", "com.unity.burst@1.8.18", "com.unity.collections@2.5.1", "com.unity.mathematics@1.3.2", "com.unity.profiling.core@1.0.2", "com.unity.editorcoroutines@1.0.0", "com.unity.2d.animation@10.1.4", "com.unity.2d.pixel-perfect@5.0.3", "com.unity.2d.psdimporter@9.0.3", "com.unity.2d.sprite@1.0.0", "com.unity.2d.spriteshape@10.0.7", "com.unity.2d.tilemap@1.0.0", "com.unity.2d.tilemap.extras@4.1.0", "com.unity.2d.aseprite@1.1.6", "com.github.asus4.tflite@2.17.0", "com.github.asus4.tflite.common@2.17.0", "com.unity.scriptablebuildpipeline@2.1.4", "com.unity.searcher@4.9.2", "com.unity.rendering.light-transport@1.0.1", "com.unity.nuget.mono-cecil@1.11.4", "com.unity.test-framework.performance@3.0.3", "com.unity.2d.common@9.0.7"], "Results": []}