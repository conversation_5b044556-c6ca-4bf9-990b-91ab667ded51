using System;
using System.Linq;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.game.setting;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.domain.settings;

namespace com.luxza.grandarts.userinterfaces.components.game.play {
    public static class GameFormatUtility
    {
        public static string GameNameText(GameCode gameCode)
        {
            return gameCode switch
            {
                GameCode._301 => "X01 301",
                GameCode._501 => "X01 501",
                GameCode._701 => "X01 701",
                GameCode._901 => "X01 901",
                GameCode._1101 => "X01 1101",
                GameCode._1501 => "X01 1501",
                GameCode._Countup => "COUNT UP",
                GameCode._Target20 => "TARGET 20",
                GameCode._HalfIt => "HALF IT",
                GameCode._Rotation => "ROTATION",
                GameCode._KickDown301 => "KICK DOWN 301",
                GameCode._KickDown501 => "KICK DOWN 501",
                GameCode._StandardCR => "STANDARD CR",
                GameCode._HiddenCR => "HIDDEN CR",
                GameCode._CutThroat => "CUT THROAT CR",
                GameCode._HiddenCutThroat => "HIDDEN CUT THROAT",
                GameCode._BeyondTop => "BEYOND TOP",
                GameCode._MultipleCr => "MULTIPLE CR.",
                _ => throw new NotImplementedException($"GameFormatText for {gameCode} is not implemented yet.")
            };
        }

        public static string GameNameShortText(GameCode gameCode)
        {
            return gameCode switch
            {
                GameCode._301 => "301",
                GameCode._501 => "501",
                GameCode._701 => "701",
                GameCode._901 => "901",
                GameCode._1101 => "1101",
                GameCode._1501 => "1501",
                GameCode._Countup => "Count Up",
                GameCode._Target20 => "Target 20",
                GameCode._HalfIt => "Half It",
                GameCode._Rotation => "Rotation",
                GameCode._KickDown301 => "Kick Down 301",
                GameCode._KickDown501 => "Kick Down 501",
                GameCode._StandardCR => "CR",
                GameCode._HiddenCR => "HIDDEN CR",
                GameCode._CutThroat => "CUT THROAT CR",
                GameCode._HiddenCutThroat => "HIDDEN CUT THROAT",
                GameCode._BeyondTop => "BEYOND TOP",
                GameCode._MultipleCr => "MULTIPLE CR.",
                _ => throw new NotImplementedException($"GameNameShortText for {gameCode} is not implemented yet.")
            };
        }

        public static string GameNameVeryShortText(GameCode gameCode)
        {
            return gameCode switch
            {
                GameCode._301 => "3",
                GameCode._501 => "5",
                GameCode._701 => "7",
                GameCode._901 => "9",
                GameCode._1101 => "11",
                GameCode._1501 => "15",
                GameCode._StandardCR => "C",
                _ => throw new NotImplementedException($"GameNameShortText for {gameCode} is not implemented yet.")
            };
        }

        public static string GameNameShortText(GameCode? gameCode)
        {
            if (gameCode == null) return "Unknown";
            return GameNameShortText(gameCode.Value);
        }

        public static string GameNameText(GameCode? gameCode)
        {
            if (gameCode == null) return "Unknown";
            return GameNameText(gameCode.Value);
        }

        public static string GameFormatText(GameCode gameCode, Legs legs)
        {
            if (legs == 1) return GameNameText(gameCode);
            return gameCode switch
            {
                GameCode._301 => $"301 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._501 => $"501 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._701 => $"701 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._901 => $"901 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._1101 => $"1101 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._1501 => $"1501 {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                GameCode._StandardCR => $"CR {legs.LegsFormat.LegsFormatText()} {legs.Value} Legs",
                _ => throw new NotImplementedException($"GameFormatText for {gameCode} is not implemented yet.")
            };
        }

        public static string InconditionText(InCondition inCondition)
        {
            return inCondition switch
            {
                InCondition.OpenIn => "S-IN",
                InCondition.DoubleIn => "D-IN",
                InCondition.MasterIn => "M-IN",
                _ => throw new NotImplementedException($"InconditionText for {inCondition} is not implemented yet.")
            };
        }

        public static string OutCondiionText(OutCondition outCondition)
        {
            return outCondition switch
            {
                OutCondition.OpenOut => "S-OUT",
                OutCondition.DoubleOut => "D-OUT",
                OutCondition.MasterOut => "M-OUT",
                _ => throw new NotImplementedException($"OutCondiionText for {outCondition} is not implemented yet.")
            };
        }

        public static string FirstLegThrowOrderText(ThrowOrderAtFirstLeg order) {
            return order switch
            {
                ThrowOrderAtFirstLeg.Cork => "Cork",
                ThrowOrderAtFirstLeg.Random => "Random",
                ThrowOrderAtFirstLeg.LooserFirst => "Looser first",
                ThrowOrderAtFirstLeg.Opponent => "Opponent first",
                ThrowOrderAtFirstLeg.Self => "You are first",
                _ => throw new NotImplementedException($"FirstLegThrowOrderText for {order} is not implemented yet.")
            };
        }

        public static string EachLegThrowOrderText(ThrowOrderAtMiddleLeg order) {
            return order switch
            {
                ThrowOrderAtMiddleLeg.LooserFirst => "Looser first",
                ThrowOrderAtMiddleLeg.TakeTurn => "Swap",
                _ => throw new NotImplementedException($"EachLegThrowOrderText for {order} is not implemented yet.")
            };
        }

        public static string HandicapSettingText(HandicapSetting handicap) {
            return handicap switch
            {
                HandicapSetting.None => "No handicaps",
                HandicapSetting.Auto => "Auto handicap",
                HandicapSetting.Manually => "Manual handicap",
                _ => throw new NotImplementedException($"HandicapSettingText for {handicap} is not implemented yet.")
            };
        }

        public static string MaxThrowText(MaxRound maxRound)
        {
            if (maxRound == 10 || maxRound == 15 || maxRound == 20 || maxRound == 25 || maxRound == 30)
            {
                return $" / Throw {maxRound * 3}";
            }
            else if (maxRound == MaxRound.Free())
            {
                return "";
            }
            else
            {
                throw new NotSupportedException($"MaxRoundText for {maxRound} is not supported.");
            }
        }
        public static string MaxRoundText(MaxRound maxRound, bool isShowRoundAtTheEnd)
        {
            if (maxRound == 10 || maxRound == 15 || maxRound == 20 || maxRound == 25 || maxRound == 30)
            {
                return $" / {maxRound.Value}{(isShowRoundAtTheEnd ? " Round" : string.Empty)}";
            }
            else if (maxRound == MaxRound.Free())
            {
                return " / No Limit";
            }
            else
            {
                throw new NotSupportedException($"MaxRoundText for {maxRound} is not supported.");
            }
        }

        public static string SetsLegsText(Sets sets, Legs legs)
        {
            if (sets == 1 && legs == 1) return string.Empty;
            if (sets == 1) return $"{legs.LegsFormat.LegsFormatText()} {legs.Value} LEGS";
            return $"{legs.LegsFormat.LegsFormatText()} {sets.Value} SETS {legs.Value} LEGS";
        }

        public static string GameFormatText(MedleyRequestFormat medleyRequestFormat)
        {
            if (medleyRequestFormat.Sets == 1 && medleyRequestFormat.Legs == 1)
            {
                return GameNameText(medleyRequestFormat.GetGameCode(1, 1));
            }

            if (medleyRequestFormat.GameCodes.All(g => g == medleyRequestFormat.GameCodes.First()))
            {
                return $"{GameNameShortText(medleyRequestFormat.GetGameCode(1, 1))} {SetsLegsText(medleyRequestFormat.Sets, medleyRequestFormat.Legs)}";
            }
            else
            {
                return $"Mix {SetsLegsText(medleyRequestFormat.Sets, medleyRequestFormat.Legs)}";
            }
        }

        public static string GameFormatTextWithOptions(x01RequestFormat format)
        {
            return $"{GameNameShortText(format.GameCode)}/{OutCondiionText(format.OutCondition)}/{InconditionText(format.InCondition)}{MaxThrowText(format.MaxRound)}";
        }

        public static string GameFormatTextWithOptions(CricketRequestFormat format)
        {
            return $"{GameNameText(format.GameCode)}{MaxRoundText(format.MaxRound, isShowRoundAtTheEnd: true)}";
        }

        public static string GameFormatTextWithOptions(OnlineRequestFormat format)
        {
            return GameFormatTextWithOptions(format.ActiveFormat.Sets, format.ActiveFormat.Legs, format.ActiveFormat);
        }

        public static string CRGameFormatTextWithOptions(CricketRequestFormat format, int currentRound)
        {
            string text = $"{GameNameText(format.GameCode)}\n";
            text += $"{currentRound}{MaxRoundText(format.MaxRound, isShowRoundAtTheEnd: false)}";
            return text;
        }

        public static string CutThroatGameFormatTextWithOptions(CutthroatRequestFormat format, int currentRound)
        {
            string text = $"{GameNameText(format.GameCode)}\n";
            text += $"{currentRound}{MaxRoundText(format.MaxRound, isShowRoundAtTheEnd: false)}";
            return text;
        }

        public static string GameFormatTextWithOptions(int setNo, int legNo, MedleyRequestFormat format)
        {
            if (format.GameCodes.All(g => g == format.GameCodes.First()))
            {
                var legsText = SetsLegsText(format.Sets, format.Legs);
                if (format.GetGameCode(setNo, legNo).Is01())
                {
                    return $"{GameNameShortText(format.GetGameCode(setNo, legNo))}/{legsText}/{OutCondiionText(format.OutCondition)}/{InconditionText(format.InCondition)}{MaxThrowText(format.MaxRound)}";
                }
                else
                {
                    return $"{GameNameShortText(format.GetGameCode(setNo, legNo))}{(legsText.Equals(string.Empty) ? string.Empty : "/")}{legsText}";
                }

            }
            else
            {
                return $"MIX/{SetsLegsText(format.Sets, format.Legs)}/{OutCondiionText(format.OutCondition)}/{InconditionText(format.InCondition)}{MaxThrowText(format.MaxRound)}";
            }
        }

        public static string MedleyCRCurrentGameFormatTextWithOptions(int set, int leg, MedleyPlayFormat format, int currentRound = 1)
        {
            var requestFormat = format.RequestFormat;
            string title = "";
            if (requestFormat.GameCodes.Count() > 1)
            {
                title += $"CR {SetsLegsText(requestFormat.Sets, requestFormat.Legs)}\n";
            }
            else
            {
                title += $"{GameNameText(requestFormat.GetGameCode(set, leg))}\n";
            }
            title += $"{currentRound}{MaxRoundText(requestFormat.MaxRound, isShowRoundAtTheEnd: false)}";

            return title;
        }

        public static string MedleyCurrentGameFormatTextWithOptions(int set, int leg, MedleyPlayFormat format, int currentRound = 1, bool isCR = false, bool isShowRoundText = false)
        {
            var requestFormat = format.RequestFormat;
            string title = "";
            if (requestFormat.GameCodes.Count() > 1)
            {
                title += $"{GameNameText(requestFormat.GetGameCode(set, leg))} {SetsLegsText(requestFormat.Sets, requestFormat.Legs)}\n";
            }
            else
            {
                title += $"{GameNameText(requestFormat.GetGameCode(set, leg))}\n";
            }

            if (isCR)
            {
                if (!isShowRoundText)
                {
                    title += $"{currentRound}{MaxRoundText(requestFormat.MaxRound, isShowRoundAtTheEnd: false)}";
                }
            }
            else
            {
                title += $"{InconditionText(requestFormat.InCondition)} / {OutCondiionText(requestFormat.OutCondition)}{MaxThrowText(requestFormat.MaxRound)}";
            }

            return title;
        }

        public static string X01GameFormatTextWithOptions(x01PlayFormat format)
        {
            var requestFormat = format.RequestFormat;
            return $"{GameNameText(requestFormat.GameCode)} \n{InconditionText(requestFormat.InCondition)} / {OutCondiionText(requestFormat.OutCondition)}{MaxThrowText(requestFormat.MaxRound)}";
        }

        public static string GameFormatTextWithOptions(KickDownRequestFormat format)
        {
            var overScoreSetting = format.OverScoreSetting == KickDownOverScoreSetting.Minus100 ? "-100" :
                format.OverScoreSetting == KickDownOverScoreSetting.Minus150 ? "-150" :
                KickDownOverScoreSetting.Random.ToString();
            return $"{GameNameShortText(format.GameCode)} / {InconditionText(format.InCondition)} / {OutCondiionText(format.OutCondition)} / {overScoreSetting}";
        }
        public static string KickDownGameFormatTextWithOptions(KickDownRequestFormat format, int currentRound)
        {
            return $"{GameNameText(format.GameCode)}\n{InconditionText(format.InCondition)}   {OutCondiionText(format.OutCondition)}   {currentRound}{MaxRoundText(format.MaxRound, isShowRoundAtTheEnd: false)}";
        }

        public static string GameFormatText(x01PlayFormat playFormat)
        {
            return GameNameText(playFormat.RequestFormat.GameCode);
        }

        public static string GameFormatText(CricketPlayFormat playFormat)
        {
            if (playFormat.RequestFormat.GameCode == GameCode._StandardCR)
            {
                return GameNameText(playFormat.RequestFormat.GameCode);
            }
            return GameNameText(playFormat.RequestFormat.GameCode);
        }

        public static string GameFormatText(int setNo, int legNo, MedleyPlayFormat playFormat)
        {
            if (playFormat.RequestFormat.Sets == 1 && playFormat.RequestFormat.Legs == 1) return GameNameText(playFormat.RequestFormat.GetGameCode(setNo, legNo));
            return GameFormatTextWithOptions(setNo, legNo, playFormat.RequestFormat);
        }

        public static string GameFormatText(CountUpPlayFormat playFormat)
        {
            return GameNameText(playFormat.RequestFormat.GameCode);
        }

        public static string GameFormatText(KickDownPlayFormat playFormat)
        {
            return GameNameText(playFormat.RequestFormat.GameCode);
        }

        public static string GameFormatText(OnlineRequestFormat playFormat)
        {
            return GameFormatText(playFormat.ActiveFormat);
        }

        public static string GameOptionsText(OnlineRequestFormat playFormat)
        {
            var x01Option = "";
            if (playFormat.ActiveFormat.GameCodes.Any(g => g.Is01()))
            {
                x01Option = $"{InconditionText(playFormat.ActiveFormat.InCondition)}/{OutCondiionText(playFormat.ActiveFormat.OutCondition)}/";
            }
            return $"{x01Option}{FirstLegThrowOrderText(playFormat.ActiveFormat.ThrowOrderAtFirstLeg)}/{EachLegThrowOrderText(playFormat.ActiveFormat.ThrowOrderAtMiddleLeg)}";///{HandicapSettingText(playFormat.ActiveFormat.HandicapSetting)}";//todo:AutoHandicapの機能復活したら戻す
        }

        #region Target 20

        public static string GameFormatTextWithOptions(Target20RequestFormat format)
        {
            if (format.GameMode == GameRuleTarget20.Target20GameMode.Normal)
            {
                return $"{GameNameShortText(format.GameCode)} / {FinishSettingText(format.TargetClearCondition)}";
            }
            return $"{GameNameShortText(format.GameCode)} / {FinishSettingText(format.TargetClearCondition)} / {OnlyText(format.GameMode)}";
        }

        public static string FinishSettingText(TargetClearCondition targetClearCondition)
        {
            return targetClearCondition switch
            {
                TargetClearCondition.Hit5 => "5 HIT",
                TargetClearCondition.Hit10 => "10 HIT",
                TargetClearCondition.Hit30 => "30 HIT",
                TargetClearCondition.Hit50 => "50 HIT",
                TargetClearCondition.Hit100 => "100 HIT",
                TargetClearCondition.Round10 => "Round 10",
                _ => throw new NotImplementedException($"FinishSettingText for {targetClearCondition} is not implemented yet.")
            };
        }

        public static string OnlyText(GameRuleTarget20.Target20GameMode gameMode)
        {
            return gameMode switch
            {
                GameRuleTarget20.Target20GameMode.OnlyS20 => "Only S20",
                GameRuleTarget20.Target20GameMode.OnlyD20 => "Only D20",
                GameRuleTarget20.Target20GameMode.OnlyT20 => "Only T20",
                _ => throw new NotImplementedException($"OnlyText for {gameMode} is not implemented yet.")
            };
        }

        #endregion
        
        #region Rotation
        
        public static string GameFormatTextWithOptions(RotationRequestFormat format)
        {
            var str = $"{GameNameShortText(format.GameCode)} / {RotationMethodText(format.RotationOption.DirectionOption)}";
            var rotationMode = RotationModeText(format.RotationOption.TargetOption);
            if (!string.IsNullOrEmpty(rotationMode))
            {
                str += $"/ {rotationMode}";
            }
            if (format.RotationOption.NoJump)
            {
                str += $"/ NoJump";
            }
            if (format.RotationOption.Continues)
            {
                str += $"/ Continues";
            }
            return str;
        }
        
        public static string RotationMethodText(RotationDirectionOption directionOption)
        {
            return directionOption switch
            {
                RotationDirectionOption.NumberAsc => "Numerical order",
                RotationDirectionOption.Clockwise => "Clockwise",
                _ => throw new NotImplementedException($"RotationMethodText for {directionOption} is not implemented yet.")
            };
        }

        public static string RotationModeText(RotationTargetOption targetOption)
        {
            return targetOption switch
            {
                RotationTargetOption.AllTarget => string.Empty,
                RotationTargetOption.SingleOnly => "Only Single",
                RotationTargetOption.DoubleOnly => "Only Double",
                RotationTargetOption.TripleOnly => "Only Triple",
                _ => throw new NotImplementedException($"RotationModeText for {targetOption} is not implemented yet.")
            };
        }
        
        #endregion

        #region  Beyond Top

        public static string GameFormatTextWithOptions(BeyondTopRequestFormat format)
        {
            return $"{GameNameShortText(format.GameCode)}{MaxRoundText(format.MaxRound, isShowRoundAtTheEnd: true)}";
        }

        #endregion
        
        #region Multiple CR

        public static string GameFormatTextWithOptions(MultipleCRRequestFormat format)
        {
            var str = $"{GameNameShortText(format.GameCode)}";
            var gameMode = MultipleCRGameModeText(format.Option);
            if (!string.IsNullOrEmpty(gameMode))
            {
                str += $"/{gameMode}";
            }
            return str;
        }
        
        private static string MultipleCRGameModeText(GameRuleMultipleCR.MultipleCROption option)
        {
            return option switch
            {
                GameRuleMultipleCR.MultipleCROption.None => string.Empty,
                GameRuleMultipleCR.MultipleCROption.TripleOut => "Only Triple",
                _ => throw new NotImplementedException($"MultipleCRGameModeText for {option} is not implemented yet.")
            };
        }

        #endregion
    }
}