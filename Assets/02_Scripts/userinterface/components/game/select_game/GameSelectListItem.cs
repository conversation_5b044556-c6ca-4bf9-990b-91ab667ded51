using R3;
using TMPro;
using UnityEngine;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.select_game;
using com.luxza.ui.components.atoms;
using com.luxza.ui.components.molecules;
using System.Linq;
using com.luxza.grandarts.userinterfaces.components.game.play;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandarts.userinterfaces.components.localize;
using com.luxza.grandarts.userinterfaces.extentions;
using com.luxza.grandarts.userinterfaces.page.parameters;

namespace com.luxza.grandarts.userinterfaces.components.game.select_game
{
    public class GameSelectListItem : MonoBehaviour
    {
        [SerializeField] private GranButton _GranButton_Item;
        [SerializeField] private GranText _GranText_Title;
        [SerializeField] private GranIcon _GranIcon;
        [SerializeField] private GameObject _GameObject_Gold;
        [SerializeField] private GranChip[] _GranChip_TagList;
        [SerializeField] private GranIconButton _GranIconButton_Download;
        [SerializeField] private GranIconButton _GranIconButton_Shop;
        [SerializeField] private GameObject _GameObject_Mark;

        private GameCode _gameCode;
        private GameTag[] _gameTags;
        private bool _isCoinGame => _gameTags.Any(v=> v.IsCoinGame());
        private bool _isNeedDownload => _gameTags.Any(v=> v.IsNeedDownload());

        private void Start()
        {
            _GranButton_Item.onClickAsObservable.Subscribe(async _ => await OnGameClicked())
                .RegisterTo(destroyCancellationToken);
        }

        private async UniTask OnGameClicked()
        {
            switch (_gameCode)
            {
                case GameCode._StandardCR:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalStandardCRGameSetting));
                    break;
                case GameCode._HiddenCR:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalHiddenCRGameSetting));
                    break;
                case GameCode._CutThroat:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalCutThroatGameSetting));
                    break;
                case GameCode._HiddenCutThroat:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalHiddenCutThroatGameSetting));
                    break;
                case GameCode._Countup:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalCountUpSetting));
                    break;
                case GameCode._Target20:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalTarget20Setting));
                    break;
                case GameCode._HalfIt:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalHalfItSetting));
                    break;
                case GameCode._Rotation:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalRotationSetting));
                    break;
                case GameCode._MultipleCr:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalMultipleCRSetting));
                    break;
                case GameCode._KickDown301:
                case GameCode._KickDown501:
                await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalKickDownSetting));
                    break;
                case GameCode._BeyondTop:
                    await PageManager.Instance.OpenAsync(new PageMeta(PageNames.LocalBeyondTopSetting));
                    break;
                default:
                    break;
            }
        }

        public void Init(GameCode gameCode)
        {
            foreach (var item in _GranChip_TagList) item.gameObject.SetActive(false);

            _gameCode = gameCode;
            _gameTags = _gameCode.GetSteelGameTags().ToArray();//todo SteelとSoftで分ける仕組みを実装する
            this._GranText_Title.text = _gameCode.LongName();

            for (int i = 0; i < _gameTags.Length; i++)
            {
                if (i >= _GranChip_TagList.Length) break;
                var tagLabel = _gameTags[i].GetLabel();

                if (tagLabel == null)
                {
                    this._GranChip_TagList[i].gameObject.SetActive(false);
                }
                else
                {
                    this._GranChip_TagList[i].gameObject.SetActive(true);
                    this._GranChip_TagList[i].label = tagLabel;
                }
            }
            //todo プレイ不可のゲームをマスクかけて押せないようにする処理
            //現状ないので一旦コメントアウト
            this._GranButton_Item.interactable = true;
            this._GameObject_Mark.SetActive(false);
            // this._GranButton_Item.interactable = !IsLock();
            // this._GameObject_Mark.SetActive(IsLock());
            this._GameObject_Gold.SetActive(_isCoinGame);
            if (_isCoinGame)
            {
                var textWidth = _GranText_Title.GetComponent<TMP_Text>().preferredWidth;
                this._GameObject_Gold.GetComponent<RectTransform>().anchoredPosition = new Vector2(
                    textWidth + 10f,
                    this._GameObject_Gold.GetComponent<RectTransform>().anchoredPosition.y);
                this._GranIconButton_Shop.gameObject.SetActive(_isCoinGame);
            }
            else
            {
                this._GranIconButton_Download.gameObject.SetActive(_isNeedDownload);
            }
            gameObject.SetActive(true);
        }
    }
}