using System;
using System.Collections.Generic;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.select_game;

namespace com.luxza.grandarts.userinterfaces.components.game.play
{
    public static class GameCodeExtensions
    {
        public static IEnumerable<GameTag> GetSoftGameTags(this GameCode gameCode)
        {
            return gameCode switch
            {
                GameCode._StandardCR => new GameTag[] { GameTag.ScoreTraining },
                GameCode._HiddenCR => new GameTag[] { GameTag.ScoreTraining },
                GameCode._CutThroat => new GameTag[] { GameTag.ScoreTraining },
                GameCode._HiddenCutThroat => new GameTag[] { GameTag.ScoreTraining },
                GameCode._Countup => new GameTag[] { GameTag.BullTraining, GameTag.ScoreTraining },
                GameCode._CrCountUp => new GameTag[] { GameTag.ScoreTraining },
                GameCode._HalfIt => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining },
                GameCode._DeltaShoot => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining },
                GameCode._Rotation => new GameTag[] { GameTag.ScoreTraining },
                GameCode._ShootForce => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining },
                GameCode._TargetBull => new GameTag[] { GameTag.BullTraining, GameTag.TargetTraining },
                GameCode._Target20 => new GameTag[] { GameTag.T20ScoreTraining, GameTag.TargetTraining },
                GameCode._MultipleCr => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining, GameTag.Paid, GameTag.Download },
                GameCode._Spider => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining, GameTag.Paid, GameTag.Download },
                GameCode._Pirates => new GameTag[] { GameTag.ScoreTraining, GameTag.TargetTraining, GameTag.Paid, GameTag.Download },
                GameCode._KickDown301 => new GameTag[] { GameTag.ScoreTraining },//todo KickDownは本来なら課金タグを入れるが
                GameCode._KickDown501 => new GameTag[] { GameTag.ScoreTraining },//課金の仕組みがないのでできたら課金タグをつける
                GameCode._BeyondTop => new GameTag[] { GameTag.ScoreTraining },
                GameCode._HyperBull => new GameTag[] { GameTag.BullTraining, GameTag.TargetTraining },
                GameCode._FunMission => new GameTag[] { GameTag.ScoreTraining, GameTag.Paid, GameTag.Download },
                GameCode._TreasureHunt => new GameTag[] { GameTag.ScoreTraining, GameTag.Paid, GameTag.Download },
                GameCode._TicTacToe => new GameTag[] { GameTag.ScoreTraining, GameTag.Paid, GameTag.Download },
                GameCode._HideAndSeek => new GameTag[] { GameTag.ScoreTraining },
                GameCode._2Line => new GameTag[] { GameTag.ScoreTraining },
                GameCode._BaseBall => new GameTag[] { GameTag.ScoreTraining, GameTag.Paid, GameTag.Download },
                _ => throw new NotImplementedException($"GetSoftGameTags for {gameCode} is not implemented yet.")
            };
        }
        public static IEnumerable<GameTag> GetSteelGameTags(this GameCode gameCode)
        {
            return gameCode switch
            {
                GameCode._StandardCR => new GameTag[] { GameTag.PositionGame },
                GameCode._HiddenCR => new GameTag[] { GameTag.PositionGame },
                GameCode._CutThroat => new GameTag[] { GameTag.PositionGame, GameTag.ForOver3Players },
                GameCode._HiddenCutThroat => new GameTag[] { GameTag.PositionGame, GameTag.ForOver3Players },
                GameCode._Countup => new GameTag[] { GameTag.ScoreTraining },
                GameCode._HalfIt => new GameTag[] { GameTag.AimTraining },
                GameCode._Rotation => new GameTag[] { GameTag.CheckoutTraining,  GameTag.AccuracyTraining},
                GameCode._Target20 => new GameTag[] { GameTag.T20ScoreTraining, GameTag.AccuracyTraining },
                GameCode._KickDown301 => new GameTag[] { GameTag.TacticalGame },
                GameCode._KickDown501 => new GameTag[] { GameTag.TacticalGame },
                GameCode._BeyondTop => new GameTag[] {GameTag.HighScore },
                GameCode._MultipleCr => new GameTag[] { GameTag.AimTraining, GameTag.TargetTraining, GameTag.Paid, GameTag.Download },
                _ => throw new NotImplementedException($"GetSteelGameTags for {gameCode} is not implemented yet.")
            };
        }
    }
}