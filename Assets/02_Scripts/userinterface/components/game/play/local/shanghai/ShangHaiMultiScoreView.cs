using System;
using System.Linq;
using com.luxza.grandarts.domains.game.context.shanghai;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandarts.domains.player;
using com.luxza.grandarts.utils;
using com.luxza.granlog;
using com.luxza.ui.components.atoms;
using R3;
using UnityEngine;
using UnityEngine.UI;
using com.luxza.grandarts.userinterfaces.game.context;
using com.luxza.grandartslogic.domain.game.round;
using com.luxza.grandartslogic.domain.game.shanghai;

namespace com.luxza.grandarts.userinterfaces.components.game.play.local.shanghai
{
    [DisallowMultipleComponent]
    public class ShangHaiMultiScoreView : MonoBehaviour
    {
        [SerializeField] private GameObject _backgroundOwner;
        [SerializeField] private RawImage _backgroundImage;
        [SerializeField] private Image _unitColor;
        [SerializeField] private GranText _gameFormatText;
        [SerializeField] private UnitIconsWithScoreListView _unitIcons;
        [SerializeField] private BasicGameContextAdoptor _adoptor;
        [SerializeField] private GranText _roundText;
        [Header("ShangHai"),SerializeField] private HalfEffect _halfEffect;
        [SerializeField] private GameObject _shangHaiEffect;

        private PlayUnit[] _playUnits;
        private GameShangHaiSessionContext _context;
        private GameRuleShangHai _rule;
        private int _currentDisplayedScore;

        private void Awake() {
            _adoptor.OnReady.Take(1).Subscribe(context => {
                _context = context as GameShangHaiSessionContext;
                if(_context == null) {
                    throw new ArgumentException("Invalid context type.");
                }
                Bind(_context.DataRetriver.BasicGameDataRetriver.PlayUnits.ToArray(), _context.DataRetriver.GameRule);
                _context.GameEventPublisher.OnStartMatch += OnStartMatch;
                _context.GameEventPublisher.OnUpdateProgress += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnRoundReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnThrowReverse += (_) => OnProgressUpdated();
                _context.GameEventPublisher.OnChange += (_) => OnThrowerChanged();
                _context.GameEventPublisher.OnOverrideThrow += (_, _) => OnProgressUpdated();
                _context.GameEventPublisher.OnEndTurn += (_) => OnEndTurn();
            }).RegisterTo(destroyCancellationToken);
        }

        public void Bind(PlayUnit[] units, GameRuleShangHai rule)
        {
            _playUnits = units;
            _rule = rule;
            _unitIcons.Bind(units, showScore: units.Length >= 1);
            _gameFormatText.text = GameFormatUtility.GameNameText(rule.Code);
        }

        private void ApplyThrower((UnitId unitId, PlayerId playerId) thrower)
        {
            _unitIcons.UpdateThrower(thrower.playerId);
            _unitColor.color = UnitColors.FromIndex(_playUnits.FirstIndex(u => u.Id == thrower.unitId));
        }

        private void OnStartMatch()
        {
            Log.i("Start match.");
            _unitIcons.UpdateThrower(_playUnits.First().HostPlayerId);
            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
            _roundText.text = $"1 / {_rule.MaxRound}";
            _halfEffect.Init();
            _shangHaiEffect.SetActive(false);
        }

        private void OnProgressUpdated() {
            if (_context.DataRetriver.BasicGameDataRetriver.CurrentRound.TryGetRoundComponent<SegmentInput>(out var segmentInput))
            {
                if (segmentInput.Throws.Count(t => !t.IsEmpty) < 3)
                {
                    _shangHaiEffect.SetActive(false);
                }
            }
            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
        }

        private void OnEndTurn()
        {
            if (!_context.DataRetriver.IsReachGameEnd)
            {
                if (_context.DataRetriver.IsShanghaiAchieved)
                {
                    _shangHaiEffect.SetActive(true);
                }
                else if (_context.DataRetriver.CurrentUnitHasHalfByCurrentRound)
                {
                    _halfEffect.Play();
                }
            }
            _unitIcons.UpdateScores(_context.DataRetriver.BasicGameDataRetriver.ScoresOfAllUnits);
        }

        private void OnThrowerChanged()
        {
            _shangHaiEffect.SetActive(false);
            var playerId = _context.DataRetriver.BasicGameDataRetriver.CurrentThrower.Id;
            _unitIcons.UpdateThrower(playerId);
            _unitColor.color = UnitColors.FromIndex(_playUnits.FirstIndex(u => u.IsMember(playerId)));
            _roundText.text = $"{_context.DataRetriver.BasicGameDataRetriver.CurrentRound.No} / {_rule.MaxRound}";
        }
    }
}
