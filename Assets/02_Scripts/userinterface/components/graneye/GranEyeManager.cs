using UnityEngine;
using com.luxza.grandarts.infrastructures.graneye;
using com.luxza.granlog;
using com.luxza.granecho.Runtime;
using com.luxza.graneye;
using com.luxza.grancamera2;

namespace com.luxza.grandarts.userinterfaces.components.graneye
{
    /// <summary>
    /// GranEye系统管理器，统一管理Echo、GranEyeCore和GranCamera2组件
    /// 作为预设放置在userinterfaces程序集中，提供统一的GranEye系统访问接口
    /// </summary>
    public class GranEyeManager : MonoBehaviour
    {
        [Header("GranEye Components")]
        [SerializeField] private EchoCore _echoCore;
        [SerializeField] private GranEyeCore _granEyeCore;
        [SerializeField] private GranCameraClient _leftCamera;
        [SerializeField] private GranCameraClient _rightCamera;
        
        [Header("Settings")]
        [SerializeField] private bool _autoInitializeOnStart = true;
        [SerializeField] private bool _enableDebugLogs = true;
        
        /// <summary>
        /// GranEye管理器单例实例
        /// </summary>
        public static GranEyeManager Instance { get; private set; }
        
        /// <summary>
        /// Echo核心组件
        /// </summary>
        public EchoCore EchoCore => _echoCore;
        
        /// <summary>
        /// GranEye核心组件
        /// </summary>
        public GranEyeCore GranEyeCore => _granEyeCore;
        
        /// <summary>
        /// 左侧相机客户端
        /// </summary>
        public GranCameraClient LeftCamera => _leftCamera;
        
        /// <summary>
        /// 右侧相机客户端
        /// </summary>
        public GranCameraClient RightCamera => _rightCamera;
        
        /// <summary>
        /// 是否已初始化
        /// </summary>
        public bool IsInitialized { get; private set; }
        
        private void Awake()
        {
            // 单例模式
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                
                if (_enableDebugLogs)
                    Log.d("[GranEyeManager] GranEye Manager initialized as singleton");
            }
            else
            {
                if (_enableDebugLogs)
                    Log.w("[GranEyeManager] Another GranEye Manager instance detected, destroying duplicate");
                Destroy(gameObject);
                return;
            }
            
            // 自动查找组件（如果未手动分配）
            AutoFindComponents();
        }
        
        private async void Start()
        {
            if (_autoInitializeOnStart)
            {
                await InitializeAsync();
            }
        }
        
        /// <summary>
        /// 自动查找GranEye相关组件
        /// </summary>
        private void AutoFindComponents()
        {
            // 查找Echo组件
            if (_echoCore == null)
            {
                _echoCore = FindObjectOfType<EchoCore>();
                if (_echoCore == null)
                {
                    // 如果找不到，尝试通过GameObject名称查找
                    var echoGameObject = GameObject.Find("Echo");
                    if (echoGameObject != null)
                    {
                        _echoCore = echoGameObject.GetComponent<EchoCore>();
                    }
                }
            }
            
            // 查找GranEyeCore组件
            if (_granEyeCore == null)
            {
                _granEyeCore = FindObjectOfType<GranEyeCore>();
            }
            
            // 查找相机组件
            if (_leftCamera == null || _rightCamera == null)
            {
                var cameras = FindObjectsOfType<GranCameraClient>();
                foreach (var camera in cameras)
                {
                    if (camera.name.Contains("Left") && _leftCamera == null)
                    {
                        _leftCamera = camera;
                    }
                    else if (camera.name.Contains("Right") && _rightCamera == null)
                    {
                        _rightCamera = camera;
                    }
                }
            }
            
            if (_enableDebugLogs)
            {
                Log.d($"[GranEyeManager] Auto-found components - Echo: {_echoCore != null}, GranEyeCore: {_granEyeCore != null}, LeftCamera: {_leftCamera != null}, RightCamera: {_rightCamera != null}");
            }
        }
        
        /// <summary>
        /// 异步初始化GranEye系统
        /// </summary>
        public async System.Threading.Tasks.Task InitializeAsync()
        {
            if (IsInitialized)
            {
                if (_enableDebugLogs)
                    Log.w("[GranEyeManager] GranEye system is already initialized");
                return;
            }
            
            try
            {
                if (_enableDebugLogs)
                    Log.d("[GranEyeManager] Starting GranEye system initialization...");
                
                // 初始化GranEyeCore
                if (_granEyeCore != null)
                {
                    await _granEyeCore.Init(transform);
                    if (_enableDebugLogs)
                        Log.d("[GranEyeManager] GranEyeCore initialized successfully");
                }
                else
                {
                    Log.w("[GranEyeManager] GranEyeCore not found, skipping initialization");
                }
                
                // 设置GranEye静态引用
                if (_granEyeCore != null)
                    GranEye.GranEyeCore = _granEyeCore;
                
                if (_echoCore != null)
                    GranEye.EchoCore = _echoCore;
                
                IsInitialized = true;
                
                if (_enableDebugLogs)
                    Log.d("[GranEyeManager] GranEye system initialization completed successfully");
            }
            catch (System.Exception ex)
            {
                Log.e($"[GranEyeManager] Failed to initialize GranEye system: {ex.Message}");
                throw;
            }
        }
        
        /// <summary>
        /// 手动设置组件引用
        /// </summary>
        public void SetComponents(EchoCore echoCore, GranEyeCore granEyeCore, GranCameraClient leftCamera, GranCameraClient rightCamera)
        {
            _echoCore = echoCore;
            _granEyeCore = granEyeCore;
            _leftCamera = leftCamera;
            _rightCamera = rightCamera;
            
            if (_enableDebugLogs)
                Log.d("[GranEyeManager] Components set manually");
        }
        
        /// <summary>
        /// 检查所有组件是否可用
        /// </summary>
        public bool AreAllComponentsAvailable()
        {
            return _echoCore != null && _granEyeCore != null && _leftCamera != null && _rightCamera != null;
        }
        
        /// <summary>
        /// 获取组件状态信息
        /// </summary>
        public string GetComponentStatus()
        {
            return $"Echo: {(_echoCore != null ? "✓" : "✗")}, " +
                   $"GranEyeCore: {(_granEyeCore != null ? "✓" : "✗")}, " +
                   $"LeftCamera: {(_leftCamera != null ? "✓" : "✗")}, " +
                   $"RightCamera: {(_rightCamera != null ? "✓" : "✗")}, " +
                   $"Initialized: {(IsInitialized ? "✓" : "✗")}";
        }
        
        private void OnDestroy()
        {
            if (Instance == this)
            {
                Instance = null;
                if (_enableDebugLogs)
                    Log.d("[GranEyeManager] GranEye Manager instance destroyed");
            }
        }
        
        /// <summary>
        /// 在Inspector中显示组件状态
        /// </summary>
        private void OnValidate()
        {
            // 这个方法在Inspector中值改变时调用，用于验证设置
        }
    }
}
