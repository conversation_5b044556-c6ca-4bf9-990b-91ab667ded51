using UnityEngine.SocialPlatforms;

namespace com.luxza.grandarts.userinterfaces.page.utils {
    public static class PageNames {

        public const string Home = "PageHome";

        public const string MatchRequest = "PageMatchRequest";
        public const string GlobalLobby = "PageGlobalLobby";
        public const string OnlineGameSetting = "PageOnlineGameSetting";
        public const string Account = "PageAccount";
        public const string SignIn = "PageSignIn";
        public const string First = "PageFirst";
        public const string ForgotPasswordInputMailAddress = "PageForgotPasswordInputMailAddress";
        public const string ForgotPasswordInputCode = "PageForgotPasswordInputCode";
        public const string ChangeEmailAddress = "PageChangeEmailAddress";
        public const string VerifyChangeEmailAddress = "PageVerifyChangeEmailAddress";
        public const string ResetPassword = "PageResetPassword";
        public const string ConfirmPassword = "PageConfirmPassword";
        public const string DeletePlayer = "PageDeletePlayer";
        public const string DeleteUserPassword = "PageDeleteUserPassword";
        public const string GranEye = "GranEye";
        public const string GranEcho = "GranEcho";
        public const string GranEyeCameraSetting = "PageCameraSetting";
        public const string GranEyeBoardSetting = "PageGranEyeBoardSetting";

        public const string GranEyeSettingTop = "PageGranEyeSettingTop";
        public const string GranEyeBoardSelect = "PageGranEyeBoardSelect";
        public const string GranEyePointCheck = "PagePointCheck";
        public const string GranEyePointCheckModel = "PagePointCheckModel";
        public const string GranEyeTargetSelect = "PageTargetSelect";

        public const string GranEyeSearchCamera = "PageSearchCamera";
        public const string GranEyeAutoCalibration = "PageAutoCalibration";
        public const string GranEyeWifiSetting = "PageWifiSetting";

        public const string GranEyeQRScanLeft = "PageGranEyeQRScanLeft";
        public const string GranEyeQRScanRight = "PageGranEyeQRScanRight";
        public const string GranEyeConfigurationQRScan = "PageGranEyeConfigurationQRScan";
        public const string GranEyeConfigurationQRGenerate = "PageGranEyeConfigurationQRGenerate";
        public const string GranEyeQRScanLedCheckLeft = "PageQRScanLedCheckLeft";
        public const string GranEyeQRScanLedCheckRight = "PageQRScanLedCheckRight";
        public const string GranEyeReCalibrationModal = "PageReCalibrationModal";
        public const string GranEyeCalibrationPointErrorModal = "CalibrationPointErrorModal";
        public const string GranEyeAIModelTest = "PageAiModelTest";
        public const string GranEyeAIModelTest2 = "PageAiModelTest2";
        public const string GranEyeSettingComp = "PageGranEyeSettingComp";
        public const string GranEyeSettingFooterSheet = "PageGranEyeSettingFooterSheet";

        public const string GranEyeAdvancedSetting = "PageGranEyeAdvancedSetting";
        public const string GranEyeAIModelSelection = "PageGranEyeAIModelSelectionView";
        public const string GranEyeAutoChange = "PageGranEyeAutoChange";

        public const string GranEyeCameraFound = "PageCameraFound";
        public const string GranEyeCameraNotFound = "PageCameraNotFound";

        public const string GranEyeFocusingLeft = "PageFocusingLeft";
        public const string GranEyeFocusingRight = "PageFocusingRight";

        public const string GranEyeBaseUnitPower = "PageBaseUnitPower";
        public const string GranEyeBaseUnitLed = "PageBaseUnitLed";

        public const string GranEyeFirmwareUpdate = "PageGranEyeFirmwareUpdate";
        public const string UpdateForEarlyUsers = "PageFW_OldVersionUpdate";

        public const string GranEyeCameraUnit = "PageCameraUnit";
        public const string GranEyeBaseUnit = "PageBaseUnit";

        public const string GranEyeAllPointsModal = "PageGranEyeAllPoints";
        public const string PageAIAllPointAdjustment = "PageAIAllPointAdjustment";
        public const string GranEyeAllPointsCheck = "PageGranEyeAllPointsCheck";
        public const string GranEyePageCalibrationExplained = "PageCalibrationExplained";
        public const string GranEyeRadiusAdjustment = "PageGranEyeRadiusAdjustment";
        public const string GranEyeSelectCalibrationPointDeviation = "PageSelectCalibrationPointDeviation";
        public const string GranEyeImageTest = "PageImageTest";
        public const string GranEyeCheck = "PageGranEyeCheck";
        public const string GranEyeStatusDescribe = "PageGranEyeStatusDescribe";

        public const string GranEyeCalibrationDescription = "PageCalibrationDescription";
        public const string GranEyeCalibrationCheckPoint = "PageCalibrationCheckPoint";
        public const string GranEyeManualSet = "PageManualSet";
        public const string GranEyeManualCalibrationCheckPoint = "PageManualCalibrationCheckPoint";
        public const string GranEyeCalibrationPointCheck = "PageCalibrationPointCheck";
        public const string GranEyeManualSetGuideModal = "PageManualSetGuideModal";
        public const string GranEyeManualSetModal = "PageManualSetModal";
        public const string PageAllPointsConfirmModal = "PageAllPointsConfirmModal";

        public const string GranEyeConnectSelect = "PageConnectSelect";
        public const string GranEyeUsbConnectGuide = "PageUsbConnectGuide";
        public const string GranEyeUSBConnectComp = "PageUSBConnectComp";
        public const string UsbCableConfirmModal = "PageUsbCableConfirmModal";

        public const string ImageAcquisitionError = "PageImageAcquisitionError";
        public const string GranEyeGameVerify = "PageGranEyeGameVerify";

        public const string GranEyeBaseUnitPowerHelpSheet = "PageGranEyeBaseUnitPowerHelpSheet";
        public const string GranEyeBaseUnitLedHelpSheet = "PageGranEyeBaseUnitLedHelpSheet";
        public const string GranEyeWifiHelpSheet = "PageGranEyeWifiHelpSheet";
        public const string GranEyeQRScanHelpSheet = "PageGranEyeQRScanHelpSheet";
        public const string GranEyeQRScanLedCheckHelpSheet = "PageGranEyeQRScanLedCheckHelpSheet";
        // public const string GranEyeFocusingHelpSheet = "PageGranEyeFocusingHelpSheet";
        public const string GranEyeFocusingConfirmModal = "PageFocusingConfirmModal";
        public const string GranEyeFocusAdjustmentGuideModal = "PageFocusAdjustmentGuideModal";
        public const string GranEyeQRGenerateGuideModal = "PageQRGenerateGuideModal";
        public const string GranEyeQRScanGuideModal = "PageQRScanGuideModal";

        public const string Menu = "PageMenu";

        public const string PlayerSearch = "PagePlayerSearch";

        public const string Language = "PageLanguage";

        public const string Country = "PageCountry";
        public const string CountrySheet = "PageCountrySheet";

        public const string BlockedPlayers = "PageBlockedPlayers";

        public const string LocalGameMenu = "LocalGameMenuSheet";
        public const string LocalRoundScoreMenu = "LocalRoundScoreMenuSheet";
        public const string LocalPlayDataMenu = "LocalPlayDataMenuSheet";
        public const string LocalTarget20RoundScoreMenu = "LocalTarget20RoundScoreMenuSheet";
        public const string LocalTarget20PlayDataMenu = "LocalTarget20PlayDataMenuSheet";
        public const string GranEyeLocalGameMenu = "GranEyeLocalGameMenuSheet";

        public const string LocalGameGranEyeEchoMenuSheet = "LocalGameGranEyeEchoMenuSheet";
        public const string LocalGameGranEyeCaribrationMenuSheet = "LocalGameGranEyeCaribrationMenuSheet";
        public const string LocalGameGranEyeStatusCheckMenuSheet = "LocalGameGranEyeStatusCheckMenuSheet";
        public const string LocalCountUpSetting = "PageLocalCountUpSetting";
        public const string LocalHalfItSetting = "PageLocalHalfItSetting";
        public const string LocalKickDownSetting = "PageLocalKickDownSetting";
        public const string LocalTarget20Setting = "PageLocalTarget20Setting";
        public const string LocalRotationSetting = "PageLocalRotationSetting";
        public const string LocalBeyondTopSetting = "PageLocalBeyondTopSetting";
        public const string LocalMultipleCRSetting = "PageLocalMultipleCRSetting";

        public const string AddInstantPlayer = "AddInstantPlayerSheet";

        public const string ChangePlayer = "PageProfileChangePlayer";

        public const string PlayerCreation = "PagePlayerCreation";

        public const string Online = "PageOnline";

        public const string LocalPlay = "PageLocalPlay";

        public const string Friend = "PageFriend";

        public const string PlayDataTop = "PagePlayDataTop";

        public const string LocalMixMedleyGameSetting = "PageLocalMixMedleyGameSetting";
        public const string LocalCricketMedleyGameSetting = "PageLocalCricketMedleyGameSetting";
        public const string LocalX01MedleyGameSetting = "PageLocalx01MedleyGameSetting";

        public const string MyPlayerProfile = "PageMyProfile";
        public const string OtherPlayerProfile = "PageOtherPlayerProfile";

        public const string Setting = "PageSetting";

        public const string DeviceSetting = "PageDeviceSetting";

        public const string Support = "PageSupport";

        public const string MatchGameSelect = "PageMatchGameSelect";

        public const string Local01GameSetting = "PageLocal01GameSetting";
        public const string GamePlaySetting = "PageGamePlaySetting";

        public const string LocalStandardCRGameSetting = "PageLocalStandardCRSetting";
        public const string LocalHiddenCRGameSetting = "PageLocalHiddenCRSetting";
        public const string LocalCutThroatGameSetting = "PageLocalCutThroatSetting";
        public const string LocalHiddenCutThroatGameSetting = "PageLocalHiddenCutThroatSetting";

        public const string CricketGameList = "PageCricketGameList";

        public const string PracticeGameList = "PagePracticeGameList";

        public const string PartyGameList = "PagePartyGameList";

        public const string LocalX01_1vs1 = "PageLocalX01_1vs1";
        public const string LocalX01_SinglesOrMulti = "PageLocalX01_SingleOrMulti";

        public const string LocalX01_1vs1_GranEye = "PageLocalX01_1vs1_GranEye";
        public const string LocalX01_SinglesOrMulti_GranEye = "PageLocalX01_SingleOrMulti_GranEye";

        public const string LocalCR_1vs1 = "PageLocalCR_1vs1";
        public const string LocalCR_SinglesOrMulti = "PageLocalCR_SingleOrMulti";

        public const string LocalCR_1vs1_GranEye = "PageLocalCR_1vs1_GranEye";
        public const string LocalCR_SinglesOrMulti_GranEye = "PageLocalCR_SingleOrMulti_GranEye";

        public const string LocalHiddenCR_1vs1 = "PageLocalHiddenCR_1vs1";
        public const string LocalHiddenCR_SinglesOrMulti = "PageLocalHiddenCR_SingleOrMulti";

        public const string LocalHiddenCR_1vs1_GranEye = "PageLocalHiddenCR_1vs1_GranEye";
        public const string LocalHiddenCR_SinglesOrMulti_GranEye = "PageLocalHiddenCR_SingleOrMulti_GranEye";

        public const string LocalCutthroat_Multi = "PageLocalCutthroatCR_Multi";
        public const string LocalCutthroat_Multi_GranEye = "PageLocalCutthroatCR_Multi_GranEye";

        public const string LocalHiddenCutthroat_Multi = "PageLocalHiddenCutthroatCR_Multi";
        public const string LocalHiddenCutthroat_Multi_GranEye = "PageLocalHiddenCutthroatCR_Multi_GranEye";

        public const string LocalCountUp_1vs1 = "PageLocalCountUp_1vs1";
        public const string LocalCountUp_SinglesOrMulti = "PageLocalCountUp_SingleOrMulti";
        public const string LocalCountUp_1vs1_GranEye = "PageLocalCountUp_1vs1_GranEye";
        public const string LocalCountUp_SinglesOrMulti_GranEye = "PageLocalCountUp_SingleOrMulti_GranEye";

        public const string LocalHalfIt_1vs1 = "PageLocalHalfIt_1vs1";
        public const string LocalHalfIt_SinglesOrMulti = "PageLocalHalfIt_SingleOrMulti";
        public const string LocalHalfIt_1vs1_GranEye = "PageLocalHalfIt_1vs1_GranEye";
        public const string LocalHalfIt_SinglesOrMulti_GranEye = "PageLocalHalfIt_SingleOrMulti_GranEye";

        public const string LocalKickDown_1vs1 = "PageLocalKickDown_1vs1";
        public const string LocalKickDown_SinglesOrMulti = "PageLocalKickDown_SingleOrMulti";

        public const string LocalKickDown_1vs1_GranEye = "PageLocalKickDown_1vs1_GranEye";
        public const string LocalKickDown_SinglesOrMulti_GranEye = "PageLocalKickDown_SingleOrMulti_GranEye";

        public const string LocalTarget20_1vs1 = "PageLocalTarget20_1vs1";
        public const string LocalTarget20_SinglesOrMulti = "PageLocalTarget20_SingleOrMulti";
        public const string LocalTarget20_1vs1_GranEye = "PageLocalTarget20_1vs1_GranEye";
        public const string LocalTarget20_SinglesOrMulti_GranEye = "PageLocalTarget20_SingleOrMulti_GranEye";
        
        public const string LocalRotation_1vs1 = "PageLocalRotation_1vs1";
        public const string LocalRotation_SinglesOrMulti = "PageLocalRotation_SingleOrMulti";
        public const string LocalRotation_1vs1_GranEye = "PageLocalRotation_1vs1_GranEye";
        public const string LocalRotation_SinglesOrMulti_GranEye = "PageLocalRotation_SingleOrMulti_GranEye";
        
        public const string LocalBeyondTop_1vs1 = "PageLocalBeyondTop_1vs1";
        public const string LocalBeyondTop_SinglesOrMulti = "PageLocalBeyondTop_SingleOrMulti";
        public const string LocalBeyondTop_1vs1_GranEye = "PageLocalBeyondTop_1vs1_GranEye";
        public const string LocalBeyondTop_SinglesOrMulti_GranEye = "PageLocalBeyondTop_SingleOrMulti_GranEye";
        
        public const string LocalMultipleCR_1vs1 = "PageLocalMultipleCR_1vs1";
        public const string LocalMultipleCR_SinglesOrMulti = "PageLocalMultipleCR_SingleOrMulti";
        public const string LocalMultipleCR_1vs1_GranEye = "PageLocalMultipleCR_1vs1_GranEye";
        public const string LocalMultipleCR_SinglesOrMulti_GranEye = "PageLocalMultipleCR_SingleOrMulti_GranEye";

        public const string GameResult_1vs1 = "PageGameResult_1vs1";
        public const string GameResult_Multi = "PageGameResult_Multi";
        public const string GameResult_Single = "PageGameResult_Single";

        public const string Checkout = "PageCheckout";
        public const string CheckoutTry = "PageCheckoutTry";

        public const string MessageModal = "MessageModal";

        public const string PlayDataOverall = "PagePlayDataOverall";

        public const string PlayDataChartX01 = "PagePlayDataChartX01";

        public const string PlayDataChartCr = "PagePlayDataChartCR";

        public const string GuestQRSheet = "PageGuestQRSheet";
        public const string GuestQRScan = "PageGuestQRScan";

        public const string WebView = "PageWebView";

        public const string MatchAccept = "PageMatchAccept";

        public const string OnlineMatchx01 = "PageOnline01";
        public const string OnlineMatchCricket = "PageOnlineCR";

        public const string OnlineResult = "PageGameOnlineResult";

        public const string ProfileReportPlayer = "PageProfileReportPlayer";
        public const string DeviceCameraSetting = "PageDeviceCameraSetting";
        public const string GranEyeCountModelHelp = "PageGranEyeCountModelHelp";
        public const string GranEyeAutoChangeModelHelp = "PageGranEyeAutoChangeModelHelp";
        public const string GranEyeAITestView = "PageGranEyeAITestView";
        public const string GranEyePredictCheckView = "PageGranEyePredictCheckView";
        public const string GranEyeAiModelTestExplanation = "PageAiModelTestExplanation";
        public const string GranEyeAiModelTestIconExplanation = "PageAiModelTestIconExplanation";

        public static readonly string[] GameAndResultPages = new[] {
            LocalX01_1vs1,
            LocalX01_SinglesOrMulti,
            LocalX01_1vs1_GranEye,
            LocalX01_SinglesOrMulti_GranEye,
            LocalCR_1vs1,
            LocalCR_SinglesOrMulti,
            LocalCR_1vs1_GranEye,
            LocalCR_SinglesOrMulti_GranEye,
            LocalHiddenCR_1vs1,
            LocalHiddenCR_SinglesOrMulti,
            LocalHiddenCR_1vs1_GranEye,
            LocalHiddenCR_SinglesOrMulti_GranEye,
            LocalCutthroat_Multi,
            LocalCutthroat_Multi_GranEye,
            LocalHiddenCutthroat_Multi,
            LocalHiddenCutthroat_Multi_GranEye,
            LocalCountUp_1vs1,
            LocalCountUp_SinglesOrMulti,
            LocalCountUp_1vs1_GranEye,
            LocalCountUp_SinglesOrMulti_GranEye,
            LocalTarget20_1vs1,
            LocalTarget20_SinglesOrMulti,
            LocalTarget20_1vs1_GranEye,
            LocalTarget20_SinglesOrMulti_GranEye,
            LocalHalfIt_1vs1,
            LocalHalfIt_SinglesOrMulti,
            LocalHalfIt_1vs1_GranEye,
            LocalHalfIt_SinglesOrMulti_GranEye,
            LocalRotation_1vs1,
            LocalRotation_SinglesOrMulti,
            LocalRotation_1vs1_GranEye,
            LocalRotation_SinglesOrMulti_GranEye,
            LocalMultipleCR_1vs1,
            LocalMultipleCR_SinglesOrMulti,
            LocalMultipleCR_1vs1_GranEye,
            LocalMultipleCR_SinglesOrMulti_GranEye,
            LocalKickDown_1vs1,
            LocalKickDown_SinglesOrMulti,
            LocalKickDown_1vs1_GranEye,
            LocalKickDown_SinglesOrMulti_GranEye,
            LocalBeyondTop_1vs1,
            LocalBeyondTop_SinglesOrMulti,
            LocalBeyondTop_1vs1_GranEye,
            LocalBeyondTop_SinglesOrMulti_GranEye,
            GameResult_1vs1,
            GameResult_Multi,
            GameResult_Single,
            OnlineMatchx01,
            OnlineMatchCricket,
            OnlineResult,
            DeviceCameraSetting
        };

    }
}