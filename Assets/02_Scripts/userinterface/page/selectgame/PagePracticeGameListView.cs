using com.luxza.grandarts.userinterfaces.components.game.select_game;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.page.utils;
using com.luxza.grandartslogic.domain.game;
using com.luxza.ui.components.organizms;
using com.luxza.ui.components.templates.layout;
using com.luxza.ui.page;
using Cysharp.Threading.Tasks;
using R3;
using UnityEngine;

namespace com.luxza.grandarts.userinterfaces.page.select_game
{
    public class PagePracticeGameListView : MonoBehaviour
    {
        [SerializeField] private GranHeader _GranHeader;
        [SerializeField] private GameSelectListItem _GameSelectListItemPrefab;
        [SerializeField] private Transform _Transform_Content;
        [SerializeField] private ZigzagLayout _ZigzagLayout;

        private GameCode[] PracticeGameCodes = new GameCode[]{
            GameCode._Countup,
            // GameCode._CrCountUp,//todo 実装されたらコメントアウト解除
            GameCode._HalfIt,
            GameCode._Rotation,
            // GameCode._TargetBull,
            GameCode._Target20,
            GameCode._MultipleCr,
            // GameCode._Spider,
            // GameCode._Pirates,
        };

        async void Start()
        {
            this._GranHeader.onClickLeftIcon1.Subscribe(async _ => await OnBackButtonClicked()).RegisterTo(destroyCancellationToken);
            _GranHeader.onClickRightIcon1.Subscribe(async _ => await OnPlaySettingClicked()).RegisterTo(destroyCancellationToken);
            foreach (Transform item in _Transform_Content) Destroy(item.gameObject);
            for (int i = 0; i < PracticeGameCodes.Length; i++)
            {
                var item = Instantiate(_GameSelectListItemPrefab, _Transform_Content);
                item.Init(PracticeGameCodes[i]);
            }
            await UniTask.DelayFrame(1);
            _ZigzagLayout.Replace();
        }

        private async UniTask OnPlaySettingClicked()
        {
            await PageManager.Instance.OpenAsync(new PageMeta(PageNames.GamePlaySetting, parameter: new PageGamePlaySettingParameter(false)));
        }
        private async UniTask OnBackButtonClicked()
        {
            await PageManager.Instance.Back();
        }
    }
}