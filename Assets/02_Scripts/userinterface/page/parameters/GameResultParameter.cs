using System;
using System.Collections.Generic;
using System.Linq;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandarts.domains.game.result.beyondtop;
using com.luxza.grandarts.domains.game.result.countup;
using com.luxza.grandarts.domains.game.result.cutthroat;
using com.luxza.grandarts.domains.game.result.halfIt;
using com.luxza.grandarts.domains.game.result.kickdown;
using com.luxza.grandarts.domains.game.result.match;
using com.luxza.grandarts.domains.game.result.match.standardcr;
using com.luxza.grandarts.domains.game.result.match.zeroone;
using com.luxza.grandarts.domains.game.result.multiplecr;
using com.luxza.grandarts.domains.game.result.rotation;
using com.luxza.grandarts.domains.game.result.target20;
using com.luxza.grandarts.domains.game.unit;
using com.luxza.ui.page;

namespace com.luxza.grandarts.userinterfaces.page.parameters {
    public class GameResultParameter : IPageParameter
    {
        public readonly ZeroOneMatchProgress x01Progress;
        public readonly StandardCRProgress standardCRProgress;
        public readonly CutthroatCRProgress cutthroatCRProgress;
        public readonly MedleyMatchProgress medleyProgress;
        public readonly CountUpProgress countUpProgress;
        public readonly Target20Progress target20Progress;
        public readonly HalfItProgress halfItProgress;
        public readonly KickdownProgress kickdownProgress;
        public readonly RotationProgress rotationProgress;
        public readonly BeyondTopProgress beyondTopProgress;
        public readonly MultipleCRProgress multipleCRProgress;

        public  GameResultParameter(ZeroOneMatchProgress progress)
        {
            x01Progress = progress;
        }

        public GameResultParameter(MedleyMatchProgress progress)
        {
            medleyProgress = progress;
        }

        public  GameResultParameter(StandardCRProgress progress)
        {
            standardCRProgress = progress;
        }

        public  GameResultParameter(CutthroatCRProgress progress)
        {
            cutthroatCRProgress = progress;
        }

        public GameResultParameter(CountUpProgress progress)
        {
            countUpProgress = progress;
        }

        public GameResultParameter(Target20Progress progress)
        {
            target20Progress = progress;
        }
        public GameResultParameter(HalfItProgress progress)
        {
            halfItProgress = progress;
        }
        
        public GameResultParameter(RotationProgress progress)
        {
            rotationProgress = progress;
        }

        public GameResultParameter(KickdownProgress progress)
        {
            kickdownProgress = progress;
        }
        
        public GameResultParameter(BeyondTopProgress progress)
        {
            beyondTopProgress = progress;
        }
        
        public GameResultParameter(MultipleCRProgress progress)
        {
            multipleCRProgress = progress;
        }

        public IEnumerable<GameResultByUnit> GetGameResultsByUnit(UnitId unitId) {
            if (x01Progress != null) return x01Progress.gameResults.SelectMany(r => r.ResultsByUnits).Where(r => r.Unit.Id == unitId);
            if (standardCRProgress != null) return standardCRProgress.gameResults.SelectMany(r => r.ResultsByUnits).Where(r => r.Unit.Id == unitId);
            if (cutthroatCRProgress != null) return cutthroatCRProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if (medleyProgress != null) return medleyProgress.gameResults.SelectMany(r => r.ResultsByUnits).Where(r => r.Unit.Id == unitId);
            if (countUpProgress != null) return countUpProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if (target20Progress != null) return target20Progress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if(kickdownProgress != null) return kickdownProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if (halfItProgress != null) return halfItProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if (beyondTopProgress != null) return beyondTopProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            if (multipleCRProgress != null) return multipleCRProgress.GameResult.ResultsByUnits.Where(r => r.Unit.Id == unitId);
            throw new ArgumentException("Invalid match progress.");
        }

        public IEnumerable<GameResultByUnit> GetLatestGameResultsByUnit()
        {
            if (x01Progress != null) return x01Progress.LatestGameResult().ResultsByUnits;
            if (standardCRProgress != null) return standardCRProgress.LatestGameResult().ResultsByUnits;
            if (cutthroatCRProgress != null) return cutthroatCRProgress.GameResult.ResultsByUnits;
            if (medleyProgress != null) return medleyProgress.GetLatestGameResultByUnits();
            if (countUpProgress != null) return countUpProgress.GameResult.ResultsByUnits;
            if (target20Progress != null) return target20Progress.GameResult.ResultsByUnits;
            if(kickdownProgress != null) return kickdownProgress.GameResult.ResultsByUnits;
            if (halfItProgress != null) return halfItProgress.GameResult.ResultsByUnits;
            if (rotationProgress != null) return rotationProgress.GameResult.ResultsByUnits;
            if (beyondTopProgress != null) return beyondTopProgress.GameResult.ResultsByUnits;
            if (multipleCRProgress != null) return multipleCRProgress.GameResult.ResultsByUnits;
            throw new ArgumentException("Invalid match progress.");
        }

        public GameResult GetLatestResult() {
            if (x01Progress != null) return x01Progress.LatestGameResult();
            if (standardCRProgress != null) return standardCRProgress.LatestGameResult();
            if (cutthroatCRProgress != null) return cutthroatCRProgress.GameResult;
            if (medleyProgress != null) return medleyProgress.LatestGameResult();
            if (countUpProgress != null) return countUpProgress.GameResult;
            if (target20Progress != null) return target20Progress.GameResult;
            if (halfItProgress != null) return halfItProgress.GameResult;
            if (rotationProgress != null) return rotationProgress.GameResult;
            if (kickdownProgress != null) return kickdownProgress.GameResult;
            if (beyondTopProgress != null) return beyondTopProgress.GameResult;
            if (multipleCRProgress != null) return multipleCRProgress.GameResult;
            throw new ArgumentException("Invalid match progress.");
        }

        public bool TryGetx01MatchProgress(out ZeroOneMatchProgress progress)
        {
            progress = x01Progress;
            return x01Progress != null;
        }

        public bool TryGetStandardCRMatchProgress(out StandardCRProgress progress)
        {
            progress = standardCRProgress;
            return standardCRProgress != null;
        }
        public bool TryGetCutthroatCRProgress(out CutthroatCRProgress progress)
        {
            progress = cutthroatCRProgress;
            return cutthroatCRProgress != null;
        }

        public bool TryGetMedleyMatchProgress(out MedleyMatchProgress progress)
        {
            progress = medleyProgress;
            return medleyProgress != null;
        }

        public bool TryGetCountUpProgress(out CountUpProgress progress)
        {
            progress = countUpProgress;
            return countUpProgress != null;
        }

        public bool TryGetHalfItProgress(out HalfItProgress progress)
        {
            progress = halfItProgress;
            return halfItProgress != null;
        }
        
        public bool TryGetRotationProgress(out RotationProgress progress)
        {
            progress = rotationProgress;
            return rotationProgress != null;
        }

        public bool TryGetKickdownProgress(out KickdownProgress progress)
        {
            progress = kickdownProgress;
            return kickdownProgress != null;
        }

        public bool TryGetTarget20Progress(out Target20Progress progress)
        {
            progress = target20Progress;
            return target20Progress != null;
        }
        
        public bool TryGetBeyondTopProgress(out BeyondTopProgress progress)
        {
            progress = beyondTopProgress;
            return beyondTopProgress != null;
        }
        
        public bool TryGetMultipleCRProgress(out MultipleCRProgress progress)
        {
            progress = multipleCRProgress;
            return multipleCRProgress != null;
        }
    }
}
