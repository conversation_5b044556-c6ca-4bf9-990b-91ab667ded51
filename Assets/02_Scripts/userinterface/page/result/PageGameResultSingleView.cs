using R3;
using UnityEngine;
using com.luxza.ui.page;
using UnityEngine.UI;
using com.luxza.ui.components.molecules;
using com.luxza.grandarts.userinterfaces.page.parameters;
using com.luxza.grandarts.userinterfaces.components.result;
using com.luxza.ui.components.atoms;
using com.luxza.grandarts.userinterfaces.page.utils;
using System;
using com.luxza.grandartslogic.domain.game;
using System.Linq;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.usecases.game;
using com.luxza.grandarts.dicontainer;
using com.luxza.grandarts.domains.game.result;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandarts.usecases.analysis;
using com.luxza.grandarts.domains.game.unit;


namespace com.luxza.grandarts.userinterfaces.page.result
{
    public class PageGameResultSingleView : MonoBehaviour
    {
        [SerializeField] private Page _page;
        [SerializeField] private GranText _title;
        [SerializeField] private GameResultUnitIcon _SingleUnitIcon;
        [SerializeField] private GranButton _btn_GameEnd;
        [SerializeField] private GranButton _btn_PlayAgain;
        [SerializeField] private GameResultDataItem _GameResultDataItemPrefab;
        [SerializeField] private ScrollRect _GameResultDataScrollRect;

        private GameResultByUnit[] _gameResults;


        void Awake()
        {
            foreach (Transform item in _GameResultDataScrollRect.content.transform) Destroy(item.gameObject);
        }

        private void Start()
        {
            _page.OnActivate.Subscribe(OnActivate).RegisterTo(destroyCancellationToken);
        }

        private void OnActivate(Unit _)
        {
            if(_page.Meta.TryGetParameter<GameResultParameter>(out var param)) {
                Init(param);
                _btn_GameEnd.onClickAsObservable.Subscribe(_ => OnClickGameEnd()).RegisterTo(destroyCancellationToken);
            } else {
                throw new ArgumentException("Invalid parameter.");
            }
        }

        private void Init(GameResultParameter param)
        {
            if (param.TryGetx01MatchProgress(out var x01Progress))
            {
                _title.text =  $"{x01Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowX01ResultData(_gameResults[0], x01Progress.PlayFormat.RequestFormat.OutCondition);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(x01Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else if (param.TryGetStandardCRMatchProgress(out var cRProgress))
            {
                _title.text =  $"{cRProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowCrResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(cRProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else if(param.TryGetCountUpProgress(out var countUpProgress)) {
                _title.text =  $"{countUpProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowCountUpResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(countUpProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            } else if(param.TryGetKickdownProgress(out var kickdownProgress)) {
                _title.text =  $"{kickdownProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowKickdownResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(kickdownProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            } else if(param.TryGetTarget20Progress(out var target20Progress)) {
                _title.text =  $"{target20Progress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowTarget20ResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(target20Progress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else if(param.TryGetHalfItProgress(out var halfItProgress)) {
                _title.text =  $"{halfItProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowHalfItResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(halfItProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else if(param.TryGetRotationProgress(out var rotationProgress)) {
                _title.text =  $"{rotationProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowRotationResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(rotationProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else if(param.TryGetMultipleCRProgress(out var multipleCRProgress)) {
                _title.text =  $"{multipleCRProgress.PlayFormat.RequestFormat.GameCode.LongName()}";
                _gameResults = param.GetLatestGameResultsByUnit().ToArray();
                if (_gameResults.Length == 1)
                {
                    ShowMultipleCRResultData(_gameResults[0]);
                    _btn_PlayAgain.onClickAsObservable.Subscribe(_ => PlayAgain(multipleCRProgress.PlayFormat)).RegisterTo(destroyCancellationToken);
                } else {
                    throw new ArgumentException("Invalid game result count.");
                }
            }
            else
            {
                throw new ArgumentException("Invalid parameter.");
            }
        }

        private void ShowGameResultData(GameResultByUnit resultByUnit, IResultItemFactory factory)
        {
            var datas = factory.Create(resultByUnit);
            foreach (var data in datas)
            {
                var Item = Instantiate(_GameResultDataItemPrefab, _GameResultDataScrollRect.content);
                Item.Bind(data);
            }

            _SingleUnitIcon.Bind(resultByUnit.Unit, false);
        }

        private void ShowX01ResultData(GameResultByUnit resultByUnit, OutCondition outCondition){
            ShowGameResultData(resultByUnit, new ZeroOneResultItemFactory(outCondition));
        }

        private void ShowCrResultData(GameResultByUnit resultByUnit){
            ShowGameResultData(resultByUnit, new CricketResultItemFactory());
        }

        private void ShowCountUpResultData(GameResultByUnit resultByUnit){
            ShowGameResultData(resultByUnit, new CountUpResultItemFactory());
        }
                
        private void ShowHalfItResultData(GameResultByUnit resultByUnit)
        {
            ShowGameResultData(resultByUnit, new HalfItResultItemFactory());
        }
        
        private void ShowRotationResultData(GameResultByUnit resultByUnit)
        {
            ShowGameResultData(resultByUnit, new RotationResultItemFactory());
        }

        private void ShowKickdownResultData(GameResultByUnit resultByUnit) {
            ShowGameResultData(resultByUnit, new KickdownResultItemFactory());
        }
        
        private void ShowTarget20ResultData(GameResultByUnit resultByUnit) {
            ShowGameResultData(resultByUnit, new Target20ResultItemFactory());
        }
        
        private void ShowMultipleCRResultData(GameResultByUnit resultByUnit) {
            ShowGameResultData(resultByUnit, new MultipleCRResultItemFactory());
        }

       private async void OnClickGameEnd()
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                await PageManager.Instance.BackSkip(PageNames.GameAndResultPages);
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(x01PlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                DIContainer.Instance.Resolve<IMatchIDService>(),
                DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalX01_SinglesOrMulti_GranEye :
                            PageNames.LocalX01_SinglesOrMulti,
                            parameter: new Localx01PageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(CricketPlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {

                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalCR_SinglesOrMulti_GranEye :
                            PageNames.LocalCR_SinglesOrMulti,
                            parameter: new LocalStandardCRPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(CountUpPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalCountUp_SinglesOrMulti_GranEye :
                            PageNames.LocalCountUp_SinglesOrMulti,
                            parameter: new LocalCountUpPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(Target20PlayFormat playFormat)
        {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalTarget20_SinglesOrMulti_GranEye :
                            PageNames.LocalTarget20_SinglesOrMulti,
                        parameter: new LocalTarget20PageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        private async void PlayAgain(HalfItPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalHalfIt_SinglesOrMulti_GranEye :
                            PageNames.LocalHalfIt_SinglesOrMulti,
                            parameter: new LocalHalfItPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(RotationPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalRotation_SinglesOrMulti_GranEye :
                            PageNames.LocalRotation_SinglesOrMulti,
                        parameter: new LocalRotationPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }

        private async void PlayAgain(KickDownPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                    DIContainer.Instance.Resolve<IMatchIDService>(),
                    DIContainer.Instance.Resolve<IEventTracker>())
                .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                        new PageMeta(
                            _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalKickDown_SinglesOrMulti_GranEye :
                            PageNames.LocalKickDown_SinglesOrMulti,
                            parameter: new LocalKickDownPageParameter(
                                matchId,
                                playFormat
                            )
                        )
                    );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
        
        private async void PlayAgain(MultipleCRPlayFormat playFormat) {
            _btn_GameEnd.loading = true;
            _btn_PlayAgain.loading = true;
            try
            {
                var matchId = await new IssueMatchIDUsecase(
                        DIContainer.Instance.Resolve<IMatchIDService>(),
                        DIContainer.Instance.Resolve<IEventTracker>())
                    .ExecuteAsync(playFormat, destroyCancellationToken);
                await PageManager.Instance.OpenAsync(
                    new PageMeta(
                        _gameResults.Select(r => r.Unit).Any(u => u.Members.Any(m => m.Settings.InputDevice == grandartslogic.InputDevice.GranEye)) ?
                            PageNames.LocalMultipleCR_SinglesOrMulti_GranEye :
                            PageNames.LocalMultipleCR_SinglesOrMulti,
                        parameter: new LocalMultipleCRPageParameter(
                            matchId,
                            playFormat
                        )
                    )
                );
            }
            finally
            {
                if (_btn_GameEnd != null)
                {
                    _btn_GameEnd.loading = false;
                }
                if (_btn_PlayAgain != null)
                {
                    _btn_PlayAgain.loading = false;
                }
            }
        }
    }
}
