using System.Threading;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.domains.player;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.game.setting
{
    public interface ILocalGameRequestFormatRepository
    {
        UniTask<x01RequestFormat> GetX01RequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);

        UniTask<x01RequestFormat> SaveX01RequestFormatAsync(PlayerId playerId, x01RequestFormat requestFormat, CancellationToken cancellationToken);

        UniTask<CricketRequestFormat> GetCricketRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);

        UniTask<CricketRequestFormat> SaveCricketRequestFormatAsync(PlayerId playerId, CricketRequestFormat requestFormat, CancellationToken cancellationToken);

        UniTask<MedleyRequestFormat> GetX01MedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);

        UniTask<MedleyRequestFormat> GetCricketMedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);

        UniTask<MedleyRequestFormat> GetMixMedleyRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);

        UniTask<MedleyRequestFormat> SaveX01MedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<MedleyRequestFormat> SaveCricketMedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<MedleyRequestFormat> SaveMixMedleyRequestFormatAsync(PlayerId playerId, MedleyRequestFormat requestFormat, CancellationToken cancellationToken);

        UniTask<KickDownRequestFormat> GetKickDownRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask<KickDownRequestFormat> SaveKickDownRequestFormatAsync(PlayerId playerId, KickDownRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<Target20RequestFormat> GetTarget20RequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask<Target20RequestFormat> SaveTarget20RequestFormatAsync(PlayerId playerId, Target20RequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<RotationRequestFormat> GetRotationRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask<RotationRequestFormat> SaveRotationRequestFormatAsync(PlayerId playerId, RotationRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<BeyondTopRequestFormat> GetBeyondTopRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask<BeyondTopRequestFormat> SaveBeyondTopRequestFormatAsync(PlayerId playerId, BeyondTopRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask<MultipleCRRequestFormat> GetMultipleCRRequestFormatAsync(PlayerId playerId, CancellationToken cancellationToken);
        UniTask<MultipleCRRequestFormat> SaveMultipleCRRequestFormatAsync(PlayerId playerId, MultipleCRRequestFormat requestFormat, CancellationToken cancellationToken);
        UniTask ClearAllSettingsAsync(PlayerId playerId, CancellationToken cancellationToken);
    }
}