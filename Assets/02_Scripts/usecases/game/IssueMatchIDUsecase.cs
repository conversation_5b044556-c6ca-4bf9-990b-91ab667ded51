using System;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.usecases.analysis;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.rotation;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.domain.settings;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.game {
    public class IssueMatchIDUsecase {
        private readonly IMatchIDService _repository;
        private readonly IEventTracker _eventTracker;
        public IssueMatchIDUsecase(IMatchIDService repository, IEventTracker eventTracker) {
            _repository = repository;
            _eventTracker = eventTracker;
        }

        public async UniTask<MatchID> ExecuteAsync(MedleyPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", string.Join(",", playFormat.RequestFormat.GameCodes.Select(c => {
                        if(!c.HasValue) return "Choice";
                        return Enum.GetName(typeof(GameCode), c);
                    }).ToArray())),
                    new("inCondition", Enum.GetName(typeof(InCondition), playFormat.RequestFormat.InCondition)),
                    new("outCondition", Enum.GetName(typeof(OutCondition), playFormat.RequestFormat.OutCondition)),
                    new("handicap", Enum.GetName(typeof(HandicapSetting), playFormat.RequestFormat.HandicapSetting)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }

        public async UniTask<MatchID> ExecuteAsync(x01PlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("inCondition", Enum.GetName(typeof(InCondition), playFormat.RequestFormat.InCondition)),
                    new("outCondition", Enum.GetName(typeof(OutCondition), playFormat.RequestFormat.OutCondition)),
                    new("handicap", Enum.GetName(typeof(HandicapSetting), playFormat.RequestFormat.HandicapSetting)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }

        public async UniTask<MatchID> ExecuteAsync(CricketPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("handicap", Enum.GetName(typeof(HandicapSetting), playFormat.RequestFormat.HandicapSetting)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
        public async UniTask<MatchID> ExecuteAsync(CutthroatPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }

        public async UniTask<MatchID> ExecuteAsync(CountUpPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }

        public async UniTask<MatchID> ExecuteAsync(HalfItPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }

        public async UniTask<MatchID> ExecuteAsync(KickDownPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("inCondition", Enum.GetName(typeof(InCondition), playFormat.RequestFormat.InCondition)),
                    new("outCondition", Enum.GetName(typeof(OutCondition), playFormat.RequestFormat.OutCondition)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
        
        public async UniTask<MatchID> ExecuteAsync(Target20PlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("finishSetting", Enum.GetName(typeof(TargetClearCondition), playFormat.RequestFormat.TargetClearCondition)),
                    new("gameMode", Enum.GetName(typeof(GameRuleTarget20.Target20GameMode), playFormat.RequestFormat.GameMode)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
        
        public async UniTask<MatchID> ExecuteAsync(RotationPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("rotationMethod", Enum.GetName(typeof(RotationDirectionOption), playFormat.RequestFormat.RotationOption.DirectionOption)),
                    new("rotationMode", Enum.GetName(typeof(RotationTargetOption), playFormat.RequestFormat.RotationOption.TargetOption)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
        
        public async UniTask<MatchID> ExecuteAsync(BeyondTopPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
        
        public async UniTask<MatchID> ExecuteAsync(MultipleCRPlayFormat playFormat, CancellationToken cancellationToken)
        {
            var matchId = await _repository.IssueAsync(playFormat, cancellationToken);
            _eventTracker.Log(LogEvent.LocalGameStarted,
                parameters: new LogEventParameter[] {
                    new("matchId", matchId.Value),
                    new("games", Enum.GetName(typeof(GameCode), playFormat.RequestFormat.GameCode)),
                    new("gameMode", Enum.GetName(typeof(GameRuleMultipleCR.MultipleCROption), playFormat.RequestFormat.Option)),
                    new("players", string.Join(",", playFormat.EntrySlots.Slots.Where(s => !s.IsEmpty()).Select(s => s.PlayerId.Value.ToString())))
                });
            return matchId;
        }
    }
}