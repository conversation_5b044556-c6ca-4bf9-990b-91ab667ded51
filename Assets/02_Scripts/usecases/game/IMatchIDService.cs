using System.Threading;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.usecases.game {
    public interface IMatchIDService {
        UniTask<MatchID> IssueAsync(MedleyPlayFormat medleyPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(x01PlayFormat x01PlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(CricketPlayFormat cricketPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(CutthroatPlayFormat cutthroatPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(CountUpPlayFormat countUpPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(HalfItPlayFormat halfItPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(KickDownPlayFormat kickDownPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(Target20PlayFormat target20PlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(RotationPlayFormat rotationPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(BeyondTopPlayFormat beyondTopPlayFormat, CancellationToken cancellationToken);
        UniTask<MatchID> IssueAsync(MultipleCRPlayFormat multipleCRPlayFormat, CancellationToken cancellationToken);
    }
}