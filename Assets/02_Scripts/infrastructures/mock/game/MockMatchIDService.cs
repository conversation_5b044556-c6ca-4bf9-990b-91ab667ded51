using System;
using System.Threading;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.usecases.game;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.mock.game
{
    public class MockMatchIDService : IMatchIDService
    {
        public UniTask<MatchID> IssueAsync(x01PlayFormat x01PlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(CricketPlayFormat cricketPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(CutthroatPlayFormat cutthroatPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }
        
        public UniTask<MatchID> IssueAsync(CountUpPlayFormat countUpPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(HalfItPlayFormat halfItPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(KickDownPlayFormat kickDownPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(Target20PlayFormat target20PlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(RotationPlayFormat rotationPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(BeyondTopPlayFormat beyondTopPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }
        
        public UniTask<MatchID> IssueAsync(MultipleCRPlayFormat multipleCRPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }

        public UniTask<MatchID> IssueAsync(MedleyPlayFormat medleyPlayFormat, CancellationToken cancellationToken)
        {
            return UniTask.FromResult(new MatchID(Guid.NewGuid().ToString()));
        }
    }
}