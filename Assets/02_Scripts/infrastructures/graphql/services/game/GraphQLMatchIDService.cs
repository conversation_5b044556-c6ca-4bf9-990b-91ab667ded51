using System;
using System.Linq;
using System.Threading;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.infrastructures.graphql.requests.game;
using com.luxza.grandarts.usecases.game;
using Cysharp.Threading.Tasks;

namespace com.luxza.grandarts.infrastructures.graphql.services.game {
    public class GraphQLMatchIDService : IMatchIDService
    {
        public UniTask<MatchID> IssueAsync(MedleyPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(x01PlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(CricketPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }
        
        public UniTask<MatchID> IssueAsync(CutthroatPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(CountUpPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }
        
        public UniTask<MatchID> IssueAsync(HalfItPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(KickDownPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(Target20PlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(RotationPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }

        public UniTask<MatchID> IssueAsync(BeyondTopPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }
        
        public UniTask<MatchID> IssueAsync(MultipleCRPlayFormat playFormat, CancellationToken cancellationToken)
        {
            if(playFormat.EntrySlots.Slots.Where(slot => !slot.IsEmpty() && slot.IsActive).All(slot => slot.PlayerId < 0)) {
                //If all players are AI or InstantPlayer, we don't need to send the data to the server. so, we don't need to issue MatchID from server.
                return UniTask.FromResult(new MatchID(new Guid().ToString()));
            }
            return new StartLocalPlayRequest(playFormat).SendAsync(cancellationToken);
        }
    }
}