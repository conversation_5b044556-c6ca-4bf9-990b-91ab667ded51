using System.Threading;
using com.luxza.grandarts.domains.exceptions;
using com.luxza.grandarts.domains.game;
using com.luxza.grandarts.domains.game.format;
using com.luxza.grandarts.infrastructures.graphql.cache;
using com.luxza.grandarts.infrastructures.graphql.client;
using com.luxza.unitygraphqlclient;
using com.luxza.unitygraphqlclient.gen;
using Cysharp.Threading.Tasks;
using com.luxza.grandarts.infrastructures.graphql.converter;
using System.Linq;

namespace com.luxza.grandarts.infrastructures.graphql.requests.game {
    public class StartLocalPlayRequest : IGraphQLRequest<MatchID>
    {
        private readonly object _parameter;

        public StartLocalPlayRequest(MedleyPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = playFormat.RequestFormat.Sets.Value,
                    isOnline = false,
                    isTournament = false,
                    setFormat = playFormat.RequestFormat.GameCodes.Select(x => x.ToGraphQLGameCode())
                }
            };
        }

        public StartLocalPlayRequest(x01PlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }

        public StartLocalPlayRequest(CricketPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }

        public StartLocalPlayRequest(CutthroatPlayFormat playFormat)
        {
            _parameter = new
            {
                input = new
                {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(CountUpPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(HalfItPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(Target20PlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(RotationPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(BeyondTopPlayFormat playFormat)
        {
            _parameter = new {
                input = new {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }

        public StartLocalPlayRequest(KickDownPlayFormat playFormat)
        {
            _parameter = new
            {
                input = new
                {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }
        
        public StartLocalPlayRequest(MultipleCRPlayFormat playFormat)
        {
            _parameter = new
            {
                input = new
                {
                    bestOfSets = 1,
                    isOnline = false,
                    isTournament = false,
                    setFormat = new[] { playFormat.RequestFormat.GameCode.ToGraphQLGameCode() }
                }
            };
        }

        public async UniTask<MatchID> SendAsync(CancellationToken cancellationToken)
        {
            IGraphQLAPIDecorator client = new GraphQLAPIClientBasicDecorator(new GraphQLAPIClient());
            client = new GraphQLAPIClientRetryDecorator(client);
            client = new RetryWithRepublishAccessTokenDecorator(client);
            client = new RetryWithLoginDecorator(client);

            if(!AuthCacheService.TryGetAuths(out var auth)) {
                throw new SessionTimeOutException("Auth is null");
            }

            var res = await client.PostAsync<CreatePlayLogResponse>(
                    new CreatePlayLogAPI(),
                    _parameter,
                    auth.accessToken,
                    cancellationToken
                );

            return new MatchID(res.create_play_log.id);
        }

        public class CreatePlayLogAPI : API
        {
            public override string Name => "CreatePlayLog";
        }

        class CreatePlayLogResponse : IGraphQLResponse
        {
            public Types.PlayLog create_play_log;
        }
    }
}