// using System.Collections.Generic;
// using com.luxza.grandarts.domains.game.setting;
// using com.luxza.grandarts.domains.graneye;
// using com.luxza.granecho.Runtime;
// using com.luxza.graneye;
//
// namespace com.luxza.grandarts.infrastructures.graneye
// {
//     public class GranEye
//     {
//         public static GranEyeCore GranEyeCore;
//         public static EchoCore EchoCore;
//         /// <summary>
//         /// GranEyeの再読み取りテスト
//         /// </summary>
// #if Production
//         public static bool IsReloadTest = false;
// #else
//         public static bool IsReloadTest = false;
// #endif
//         public static bool IsEchoConnected = false;
//         public static List<int> PointsOrderList => new List<int>() { 2, 3, 1, 4 };
//         /// <summary>
//         /// When opening the GranEye camera settings for the first time, use
//         /// </summary>
//         public static bool IsGuide => GranEyeSettingSaver.IsGuide;
//
//         public static bool IsUSB => GranEyeCore == null ? false : GranEyeCore.Get_USBMode();
//
//
//         
//         public static string CurrentLeftCameraVersion = string.Empty;
//         public static string CurrentRightCameraVersion = string.Empty;
//         
//         public static string CurrentEchoVersion = string.Empty;
//         
//         public static (FirmwareVersionEntity latestVersion, FirmwareVersionEntity? forceVersion) GranEyeCameraNewVersion = default;
//         public static (FirmwareVersionEntity latestVersion, FirmwareVersionEntity? forceVersion) GranEyeEchoNewVersion = default;
//         private static bool _isLatestVersionUpdate = true;
//         
//         public static void TestCameraVersion()
//         {
//             // TODO:GranEye Camera Version Test
//             // GranEyeCameraNewVersion.forceVersion = new FirmwareVersionEntity()
//             // {
//             //     Version = "1.2.1",
//             // };
//             // GranEyeCameraNewVersion.latestVersion.Version = "1.2.2";
//         }
//         
//         public static void TestEchoVersion()
//         {
//             // TODO:GranEye Echo Version Test
//             // CurrentEchoVersion = "GE1127";
//             // GranEyeEchoNewVersion.forceVersion = new FirmwareVersionEntity()
//             // {
//             //     Version = "1.1.28",
//             // };
//             // GranEyeEchoNewVersion.latestVersion.Version = "1.1.28";
//         }
//
//         public static bool IsHaveCameraForceVersion()
//         {
//             if (GranEyeCameraNewVersion == default || (GranEyeCameraNewVersion != default && GranEyeCameraNewVersion.forceVersion == null))
//             {
//                 return false;
//             }
//             if(string.IsNullOrEmpty(CurrentLeftCameraVersion) && string.IsNullOrEmpty(CurrentRightCameraVersion))
//             {
//                 return false;
//             }
//             var forceVersionInt = int.Parse(GranEyeCameraNewVersion.forceVersion.Version.Replace(".", ""));
//             if (!string.IsNullOrEmpty(CurrentLeftCameraVersion))
//             {
//                 var currentLeftCameraVersionInt = int.Parse(CurrentLeftCameraVersion.Replace("Ver.", "").Replace(".", ""));
//                 if (currentLeftCameraVersionInt < forceVersionInt)
//                 {
//                     return true;
//                 }
//             }
//             if (!string.IsNullOrEmpty(CurrentRightCameraVersion))
//             {
//                 var currentRightCameraVersionInt = int.Parse(CurrentRightCameraVersion.Replace("Ver.", "").Replace(".", ""));
//                 if (currentRightCameraVersionInt < forceVersionInt)
//                 {
//                     return true;
//                 }
//             }
//             return false;
//         }
//         
//         public static bool IsHaveEchoForceVersion()
//         {
//             if (GranEyeEchoNewVersion == default || (GranEyeEchoNewVersion != default && GranEyeEchoNewVersion.forceVersion == null))
//             {
//                 return false;
//             }
//             if(string.IsNullOrEmpty(CurrentEchoVersion))
//             {
//                 return false;
//             }
//             var currentVersionInt = int.Parse(CurrentEchoVersion.Replace("GE","").Replace(".", ""));
//             var forceVersionInt = int.Parse(GranEyeEchoNewVersion.forceVersion.Version.Replace(".", ""));
//             if (currentVersionInt < forceVersionInt)
//             {
//                 return true;
//             }
//             return false;
//         }
//
//         public static bool IsHaveCameraLatestVersion()
//         {
//             if (GranEyeCameraNewVersion == default)
//             {
//                 return false;
//             }
//             if(string.IsNullOrEmpty(CurrentLeftCameraVersion) && string.IsNullOrEmpty(CurrentRightCameraVersion))
//             {
//                 return false;
//             }
//             var latestVersionInt = int.Parse(GranEyeCameraNewVersion.latestVersion.Version.Replace(".", ""));
//             if (!string.IsNullOrEmpty(CurrentLeftCameraVersion))
//             {
//                 var currentLeftCameraVersionInt = int.Parse(CurrentLeftCameraVersion.Replace("Ver.", "").Replace(".", ""));
//                 if (currentLeftCameraVersionInt < latestVersionInt && _isLatestVersionUpdate)
//                 {
//                     _isLatestVersionUpdate = false;
//                     return true;
//                 }
//             }
//             if (!string.IsNullOrEmpty(CurrentRightCameraVersion))
//             {
//                 var currentRightCameraVersionInt = int.Parse(CurrentRightCameraVersion.Replace("Ver.", "").Replace(".", ""));
//                 if (currentRightCameraVersionInt < latestVersionInt && _isLatestVersionUpdate)
//                 {
//                     _isLatestVersionUpdate = false;
//                     return true;
//                 }
//             }
//             return false;
//         }
//
//         public static bool IsHaveEchoLatestVersion()
//         {
//             if (GranEyeEchoNewVersion == default)
//             {
//                 return false;
//             }
//             if(string.IsNullOrEmpty(CurrentEchoVersion))
//             {
//                 return false;
//             }
//             var currentVersionInt = int.Parse(CurrentEchoVersion.Replace("GE","").Replace(".", ""));
//             var latestVersionInt = int.Parse(GranEyeEchoNewVersion.latestVersion.Version.Replace(".", ""));
//             if (currentVersionInt < latestVersionInt && _isLatestVersionUpdate)
//             {
//                 _isLatestVersionUpdate = false;
//                 return true;
//             }
//             return false;
//         }
//
//         public static int MaxGetTexCount => 3;
//         
//         public static string[] BoardBrandList => new string[] {
//             "GRAN DARTS",
//             "Winmau",
//             "Unicorn",
//             "Mission",
//             "TARGET",
//             "One80",
//             "L-style",
//             "Other"
//         };
//     }
// }