using com.luxza.granlog;

namespace com.luxza.grandarts.infrastructures.graneye
{
    /// <summary>
    /// EchoCore适配器类，为userinterfaces程序集提供EchoCore功能的访问
    /// 解决程序集引用问题，避免userinterfaces直接依赖granecho包
    /// </summary>
    public static class EchoCoreAdapter
    {
        /// <summary>
        /// 检查设备蓝牙是否开启
        /// </summary>
        /// <returns>蓝牙是否开启</returns>
        public static bool IsBluetoothEnabledOnDevice()
        {
            try
            {
                if (GranEye.EchoCore == null)
                {
                    Log.w("[EchoCoreAdapter] EchoCore is null, returning false for Bluetooth status");
                    return false;
                }
                
                return GranEye.EchoCore.IsBluetoothEnabledOnDevice();
            }
            catch (System.Exception ex)
            {
                Log.e($"[EchoCoreAdapter] Error checking Bluetooth status: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// 搜索并连接Echo设备
        /// </summary>
        public static void SearchAndConnect()
        {
            try
            {
                if (GranEye.EchoCore == null)
                {
                    Log.w("[EchoCoreAdapter] EchoCore is null, cannot search and connect");
                    return;
                }
                
                Log.d("[EchoCoreAdapter] Starting Echo search and connect");
                GranEye.EchoCore.SearchAndConnect();
            }
            catch (System.Exception ex)
            {
                Log.e($"[EchoCoreAdapter] Error during search and connect: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 打开系统蓝牙设置页面
        /// </summary>
        public static void OpenBluetoothSettings()
        {
            try
            {
                if (GranEye.EchoCore == null)
                {
                    Log.w("[EchoCoreAdapter] EchoCore is null, cannot open Bluetooth settings");
                    return;
                }
                
                Log.d("[EchoCoreAdapter] Opening Bluetooth settings");
                GranEye.EchoCore.OpenBluetoothSettings();
            }
            catch (System.Exception ex)
            {
                Log.e($"[EchoCoreAdapter] Error opening Bluetooth settings: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 断开Echo连接
        /// </summary>
        public static void Disconnect()
        {
            try
            {
                if (GranEye.EchoCore == null)
                {
                    Log.w("[EchoCoreAdapter] EchoCore is null, cannot disconnect");
                    return;
                }
                
                Log.d("[EchoCoreAdapter] Disconnecting Echo");
                GranEye.EchoCore.Disconnect();
            }
            catch (System.Exception ex)
            {
                Log.e($"[EchoCoreAdapter] Error during disconnect: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 获取Echo版本信息
        /// </summary>
        public static void GetVersion()
        {
            try
            {
                if (GranEye.EchoCore == null)
                {
                    Log.w("[EchoCoreAdapter] EchoCore is null, cannot get version");
                    return;
                }
                
                Log.d("[EchoCoreAdapter] Getting Echo version");
                GranEye.EchoCore.GetVersion();
            }
            catch (System.Exception ex)
            {
                Log.e($"[EchoCoreAdapter] Error getting version: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 检查EchoCore是否可用
        /// </summary>
        /// <returns>EchoCore是否可用</returns>
        public static bool IsEchoCoreAvailable()
        {
            return GranEye.EchoCore != null;
        }
    }
}
