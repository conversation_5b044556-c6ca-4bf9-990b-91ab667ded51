using System.Collections.Generic;
using UnityEngine;
using com.luxza.granecho.Runtime;
using com.luxza.graneye;
using com.luxza.granlog;

namespace com.luxza.grandarts.infrastructures.graneye
{
    /// <summary>
    /// 测试GranEye新方法的编译
    /// </summary>
    public static class GranEyeTest
    {
        public static void TestMethods()
        {
            // 测试蓝牙方法
            bool bluetoothEnabled = GranEye.IsBluetoothEnabled();
            GranEye.SearchAndConnectEcho();
            GranEye.OpenBluetoothSettings();
            GranEye.DisconnectEcho();
            GranEye.GetEchoVersion();
            
            // 测试事件监听
            GranEye.AddEchoConnectionListener(OnEchoConnectionChanged);
            GranEye.RemoveEchoConnectionListener(OnEchoConnectionChanged);
            GranEye.AddEchoDataListener(OnEchoDataReceived);
            GranEye.RemoveEchoDataListener(OnEchoDataReceived);
            
            // 测试USB方法
            _ = TestUSBMethods();
        }
        
        private static async System.Threading.Tasks.Task TestUSBMethods()
        {
            bool searchResult = await GranEye.SearchUSBDevices();
            Dictionary<int, bool> connectResult = await GranEye.ConnectUSBDetailed();
            bool echoConnectResult = await GranEye.ConnectEchoAsync();
            bool cameraConnectResult = await GranEye.ConnectCamerasAsync();
            
            GranEye.USBConnectLEDAction();
            GranEye.StopStream();
            GranEye.SendGetMacCommand(1);
            GranEye.GetPictures();
            GranEye.DisconnectUSB();
            GranEye.SetUSBMode(true);
        }
        
        private static void OnEchoConnectionChanged(bool connected)
        {
            Log.d($"Echo connection changed: {connected}");
        }
        
        private static void OnEchoDataReceived(string data)
        {
            Log.d($"Echo data received: {data}");
        }
    }
}
