using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.round;

namespace com.luxza.grandarts.domains.game.factory
{
    public static class RoundInputTypeConverter {
        public static RoundInputType ConvertToSupportedInputType(this RoundInputType roundInputType, GameCode code, bool useGranEye) {
            if(useGranEye) {
                return RoundInputType.Segment;
            }

            return code switch
            {
                GameCode._301 => roundInputType,
                GameCode._501 => roundInputType,
                GameCode._701 => roundInputType,
                GameCode._901 => roundInputType,
                GameCode._1101 => roundInputType,
                GameCode._1501 => roundInputType,
                GameCode._Freeze301 => roundInputType,
                GameCode._Freeze501 => roundInputType,
                GameCode._Freeze701 => roundInputType,
                GameCode._Freeze901 => roundInputType,
                GameCode._Freeze1101 => roundInputType,
                GameCode._Freeze1501 => roundInputType,
                GameCode._KickDown301 => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._KickDown501 => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._StandardCR => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._HiddenCR => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._CutThroat => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._HiddenCutThroat => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TeamCR => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._WildCardCR => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Countup => roundInputType,
                GameCode._CrCountUp => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._HalfIt => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._DeltaShoot => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TargetHat => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TargetChoice => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Oniren => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TargetBull => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Target20 => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._ShootForce => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TargetHorse => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._MultipleCr => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Pirates => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Spider => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._Rotation => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._BeyondTop => roundInputType,
                GameCode._HyperBull => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TreasureHunt => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._2Line => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._HideAndSeek => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._FunMission => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._TicTacToe => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._ANIMAL_301GAME => roundInputType,
                GameCode._ANIMAL_501GAME => roundInputType,
                GameCode._ANIMAL_701GAME => roundInputType,
                GameCode._ANIMAL_901GAME => roundInputType,
                GameCode._ANIMAL_1101GAME => roundInputType,
                GameCode._ANIMAL_1501GAME => roundInputType,
                GameCode._ANIMAL_STANDARD_CR => grandartslogic.domain.game.round.RoundInputType.Segment,
                GameCode._BaseBall => grandartslogic.domain.game.round.RoundInputType.Segment,
            };
        }
    }
}