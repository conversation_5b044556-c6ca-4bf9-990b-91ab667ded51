using com.luxza.grandarts.domains.game.format;
using com.luxza.grandartslogic.domain.game;
using com.luxza.grandartslogic.domain.game.beyondtop;
using com.luxza.grandartslogic.domain.game.countup;
using com.luxza.grandartslogic.domain.game.KickDown;
using com.luxza.grandartslogic.domain.game.cricket;
using com.luxza.grandartslogic.domain.game.zeroone;
using com.luxza.grandartslogic.domain.game.cutthroat;
using com.luxza.grandartslogic.domain.game.target20;
using com.luxza.grandartslogic.domain.game.halfit;
using com.luxza.grandartslogic.domain.game.multiplecr;
using com.luxza.grandartslogic.domain.game.rotation;

namespace com.luxza.grandarts.domains.game.factory
{
    public class GameRuleFactory
    {
        public static GameRule01 Create(x01RequestFormat requestFormat)
        {
            return new GameRule01(
                requestFormat.GameCode,
                maxRound: requestFormat.MaxRound,
                inCondition: requestFormat.InCondition,
                outCondition: requestFormat.OutCondition,
                //TODO: not support fatbull yet.
                bullOption: BullOption.SeparateBull,
                handicap: requestFormat.HandicapSetting
            );
        }

        public static GameRuleCountUp Create(CountUpRequestFormat requestFormat)
        {
            return new GameRuleCountUp(
                maxRound: requestFormat.MaxRound,
                option: BullOption.SeparateBull
            );
        }

        public static GameRuleKickDown Create(KickDownRequestFormat requestFormat)
        {
            return new GameRuleKickDown(
                requestFormat.GameCode,
                maxRound: requestFormat.MaxRound,
                inCondition: requestFormat.InCondition,
                outCondition: requestFormat.OutCondition,
                KickDownOverScoreSetting: requestFormat.OverScoreSetting,
                option: BullOption.SeparateBull
            );
        }

        public static GameRuleStandardCR Create(CricketRequestFormat requestFormat)
        {
            return new GameRuleStandardCR(
                maxRound: requestFormat.MaxRound,
                handicap: requestFormat.HandicapSetting,
                markDisplayOption: requestFormat.MarkDisplayOption
            );
        }
        
        public static GameRuleCutThroat Create(CutthroatRequestFormat requestFormat)
        {
            return new GameRuleCutThroat(
                maxRound: requestFormat.MaxRound,
                markDisplayOption: requestFormat.MarkDisplayOption
            );
        
        }

        public static GameRuleHalfIt Create(HalfItRequestFormat requestFormat)
        {
            return new GameRuleHalfIt();
        }
        
        public static GameRuleTarget20 Create(Target20RequestFormat requestFormat)
        {
            return new GameRuleTarget20(
                target20GameModeOption: requestFormat.GameMode,
                clearCondition: requestFormat.TargetClearCondition
            );
        }
        
        public static GameRuleRotation Create(RotationRequestFormat requestFormat)
        {
            var rotationOption = requestFormat.RotationOption;
            return new GameRuleRotation(option: rotationOption);
        }
        
        public static GameRuleBeyondTop Create(BeyondTopRequestFormat requestFormat)
        {
            return new GameRuleBeyondTop(requestFormat.MaxRound);
        }
        
        public static GameRuleMultipleCR Create(MultipleCRRequestFormat requestFormat)
        {
            var option = requestFormat.Option;
            return new GameRuleMultipleCR(multipleCROption: option);
        }
    }
}