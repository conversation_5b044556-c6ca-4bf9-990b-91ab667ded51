using com.luxza.grandarts.domains.game.unit;
using com.luxza.grandartslogic.domain.game.shanghai;

namespace com.luxza.grandarts.domains.game.context.shanghai
{
    public class ShangHaiGameDataRetriver : IGameDataRetriver<GameRuleShangHai>
    {
        public BasicGameDataRetriver BasicGameDataRetriver { get; }

        private readonly RefereeShang<PERSON><PERSON> _referee;
        
        public GameRuleShangHai GameRule => _referee.Match.Rule;

        public ShangHaiGameDataRetriver(RefereeShangHai referee, PlayUnit[] units)
        {
            BasicGameDataRetriver = new BasicGameDataRetriver(referee, units);
            _referee = referee;
        }

        public void Dispose()
        {
            BasicGameDataRetriver.Dispose();
        }

        public bool CurrentUnitHasHalfByCurrentRound =>
            _referee.Scorer.HasHalfPenalty(_referee.CurrentThrowingUnitId, _referee.CurrentRoundAtCurrentTeam.No);

        public bool IsShanghaiAchieved =>
            _referee.Scorer.IsShanghaiAchieved(_referee.CurrentThrowingUnitId, _referee.CurrentRoundAtCurrentTeam.No - 1);

        public bool IsReachGameEnd => _referee.IsReachGameEnd;

        public int ThrowsPerRound => _referee.Match.Rule.ThrowsPerRound;
    }
}