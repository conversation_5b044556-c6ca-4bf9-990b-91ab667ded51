using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using com.luxza.granecho.Runtime;
using com.luxza.graneye.exception;
using Cysharp.Threading.Tasks;
using UnityEngine;

namespace com.luxza.graneye
{
    public class GranEye
    {
        public enum  GranReactiveAction
        {
            // MainCameraView,
            LeftCameraView,
            RightCameraView,
            Echo_Connected,
            Echo_Disconnected,
            Auto_Calibration,
        }
        public class GranEyeEntity
        {
            public GranReactiveAction Action;
            public bool Result;
            public Texture CameraTexture;
            public List<Vector3> DotList;
        }
        
        private static GranEye _instance;
        
        public static GranEye Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new GranEye();
                }
                return _instance;
            }
        }
        
        
        
        
        
        
        
        
        private GranEyeCore m_GranEyeCore;
        private EchoCore m_EchoCore;
        
        public string CurrentEchoVersion {get; private set;} = string.Empty;
        public bool IsEchoConnected { get; private set; }= false;

        private GameObject m_Self;
        private GranEye()
        {

            this.m_Self = new GameObject("GranEyeHelper");
            GameObject.DontDestroyOnLoad(m_Self);
            
            this.m_GranEyeCore = new GranEyeCore();
            this.m_EchoCore = new GameObject("GranEcho").AddComponent<EchoCore>();
            this.m_EchoCore.transform.SetParent(m_Self.transform);
            
            this.m_EchoCore.On_EchoConnectionChanged_Callback.AddListener(OnEchoConnectionChanged);
            this.m_EchoCore.On_EchoReceived_Callback.AddListener(OnReceiveEchoData);
        }

        public bool Init()
        {
            this.m_GranEyeCore.Init(m_Self.GetComponent<RectTransform>());

            return true;
        }



        // /// <summary>
        // /// 
        // /// </summary>
        // /// <returns></returns>
        // public async Task<GranEyeEntity> GetMainCameraViewData()
        // {
        //     var t_Ret = await this.m_GranEyeCore.GetImage(GranEyeCore.CameraPos.Left);
        //     var t_Entity = new GranEyeEntity()
        //     {
        //         Action = GranReactiveAction.MainCameraView,
        //         LeftCameraTexture = t_Ret.Texture
        //     };
        //     return t_Entity;
        // }
        
        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public async Task<GranEyeEntity> GetCameraViewData(GranEyeCore.CameraPos cameraPos)
        {
            var t_Ret = await this.m_GranEyeCore.GetImage(cameraPos);
            var t_Entity = new GranEyeEntity()
            {
                Action = cameraPos == GranEyeCore.CameraPos.Left ? GranReactiveAction.LeftCameraView : GranReactiveAction.RightCameraView
            };
            t_Entity.Result = t_Ret.IsSuccess;
            if (t_Ret.IsSuccess)
            {
                t_Entity.CameraTexture = t_Ret.Texture;
            }
            return t_Entity;
        }
        
        #region Echo

        private async void OnEchoConnectionChanged(bool success)
        {
            IsEchoConnected = success;
          
            if (success)
            {
                if (string.IsNullOrEmpty(CurrentEchoVersion))
                {
                    await UniTask.Delay(1500);
                    m_EchoCore.GetVersion();
                }
            }
        }

        private void OnReceiveEchoData(string data)
        {
            if (data.Contains("GE"))
            {
                CurrentEchoVersion = data.Replace("GE","");
            }
        }

        #endregion
        
        #region Calibration

        public async UniTask<bool> AutomaticCalibration(GranEyeCore.CameraPos cameraPos, Texture texture)
        {
            // var culture = CultureInfo.InvariantCulture;
            var cancellationPosList = new Dictionary<int, Vector3>();
            try
            {
                // Log.d($"{cameraPos}, Start auto calibration");
                var posInfoList = new Dictionary<int, TFEntities.PredictResultPointEntity>();

                List<TFEntities.PredictResultPointEntity> tPoints;
                tPoints = await m_GranEyeCore.PredictBoard(texture);

                if (tPoints == null || !tPoints.Any())
                {
                    // Log.w("tPoints is empty or null");
                    return false;
                }

                for (var i = 0; i < tPoints.Count; i++)
                {
                    if (posInfoList.ContainsKey(tPoints[i].Tag))
                    {
                        if (posInfoList[tPoints[i].Tag].score < tPoints[i].score)
                            posInfoList[tPoints[i].Tag] = tPoints[i];
                    }
                    else
                    {
                        posInfoList.Add(tPoints[i].Tag, tPoints[i]);
                    }
                }
            
                posInfoList = posInfoList.OrderBy(x => x.Key).ToDictionary(x => x.Key, x => x.Value);
            
                var tipStr = string.Empty;
            
                foreach (var pointInfo in posInfoList)
                {
                    var pos = TflitePositionHelper.ModelPos2UnityPos(new Vector2(pointInfo.Value.x, pointInfo.Value.y));
                    // Log.d($"{pointInfo.Key},x = {pos.x},y = {pos.y}");
                    tipStr += $"tag{pointInfo.Key}, x:{pointInfo.Value.x:0.0}, y:{pointInfo.Value.y:0.0}, score:{pointInfo.Value.score * 100:0.0}%\n";
                
                    m_GranEyeCore.Set_Dot(cameraPos, CalibrationType.Auto, pointInfo.Key - 1, pos);
                    cancellationPosList.Add(pointInfo.Key, pos);
                }

                if (cancellationPosList.Count == 4)
                {
                    return m_GranEyeCore.Calibration(cameraPos, cancellationPosList.Values.ToList());
                }
                else
                {
                    // Log.e($"Calibration failed, only {cancellationPosList.Count} points detected.\n{tipStr}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"An error occurred: {ex.Message}\n{ex.StackTrace}");
                return false;
            }
        }

        public List<Vector3> GetAllPointsOnRealBoard(GranEyeCore.CameraPos cameraPos)
        {
            return m_GranEyeCore.GetCalibrationTool(cameraPos).GetAllPointsOnRealBoard();
        }

        public void DeleteAllDots(GranEyeCore.CameraPos cameraPos)
        {
            for (int i = 0; i < 4; i++)
            {
                m_GranEyeCore.Delete_Dot(cameraPos, CalibrationType.Auto, i);
            }
        }

        public GranEyeEntity Get_DotList(GranEyeCore.CameraPos cameraPos)
        {
            var list = new List<Vector3>();
            for (int i = 0; i < 4; i++)
            {
                list.Add(m_GranEyeCore.Get_Dot(cameraPos, CalibrationType.Auto, i));
            }
            var t_Entity = new GranEyeEntity()
            {
                Action = cameraPos == GranEyeCore.CameraPos.Left ? GranReactiveAction.LeftCameraView : GranReactiveAction.RightCameraView,
                DotList = list
            };
            return t_Entity;
        }

        #endregion
    }
}