using System;
using com.luxza.grancamera2.Interface;
using TensorFlowLite;
using UnityEngine;
using com.luxza.granlog;
using System.Globalization;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        public void Set_AcceleratorType(string acceleratorType)
        {
            PlayerPrefs.SetString("AcceleratorType", acceleratorType);
            Log.d($"AcceleratorType, {acceleratorType}");
        }

        #region USB模式相关
        /// <summary>
        /// 设置USB模式状态
        /// </summary>
        /// <param name="isUSBMode">是否为USB模式</param>
        public void Set_USBMode(bool isUSBMode)
        {
            PlayerPrefs.SetInt("IsUSBMode", isUSBMode ? 1 : 0);
            Debug.Log($"已保存USB模式状态: {isUSBMode}");
        }

        /// <summary>
        /// 获取USB模式状态
        /// </summary>
        /// <returns>是否为USB模式</returns>
        public bool Get_USBMode()
        {
            return PlayerPrefs.GetInt("IsUSBMode", 0) == 1;
        }
        
        public void DeleteUSBMode()
        {
            PlayerPrefs.DeleteKey("IsUSBMode");
        }
        #endregion

        #region 4点定位相关

        public void Set_Dot(CameraPos cameraPos, CalibrationType type, int dotIndex, Vector2 dotPos)
        {
            PlayerPrefs.SetString($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Xpos", dotPos.x.ToString(CultureInfo.InvariantCulture));
            PlayerPrefs.SetString($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Ypos", dotPos.y.ToString(CultureInfo.InvariantCulture));
        }

        public Vector2 Get_Dot(CameraPos cameraPos, CalibrationType type, int dotIndex)
        {
            float.TryParse(PlayerPrefs.GetString($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Xpos", "0"), NumberStyles.Float, CultureInfo.InvariantCulture, out var xPos);
            float.TryParse(PlayerPrefs.GetString($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Ypos", "0"), NumberStyles.Float, CultureInfo.InvariantCulture, out var yPos);

            Debug.Log($"Get Pos X: {xPos}, Y: {yPos}");
            return new Vector2(xPos, yPos);
        }

        public void Delete_Dot(CameraPos cameraPos, CalibrationType type, int dotIndex)
        {
            PlayerPrefs.DeleteKey($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Xpos");
            PlayerPrefs.DeleteKey($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Ypos");
        }

        public bool Has_Dot(CameraPos cameraPos, CalibrationType type, int dotIndex)
        {
            return PlayerPrefs.HasKey($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Xpos") &&
                   PlayerPrefs.HasKey($"IpCamera_{cameraPos}_{type}_{dotIndex}Dot_Ypos");
        }

        public void Set_Board4Point_Conf(float value)
        {
            PlayerPrefs.SetFloat("Board4Point_Conf", value);
        }

        public float Get_Board4Point_Conf()
        {
            return PlayerPrefs.GetFloat("Board4Point_Conf", 0.1f);
        }

        public void Set_Board4Point_IoU(float value)
        {
            PlayerPrefs.SetFloat("Board4Point_IoU", value);
        }

        public float Get_Board4Point_IoU()
        {
            return PlayerPrefs.GetFloat("Board4Point_IoU", 0.7f);
        }

        #endregion


        #region 相机相关
        public ICamera.E_ResolutionSize Get_CameraResolution()
        {
            return (ICamera.E_ResolutionSize)Enum.Parse(typeof(ICamera.E_ResolutionSize), PlayerPrefs.GetString($"IpCamera_CameraResolution", "Low"));
        }

        public void Set_IpCameraAddress(CameraPos cameraPos, string address)
        {
            Log.d($"Set IpCamera_{cameraPos}_Address, {address}");
            PlayerPrefs.SetString($"IpCamera_{cameraPos}_Address", address);
        }

        public string Get_IpCameraAddress(CameraPos cameraPos)
        {
            Log.d($"Get IpCamera_{cameraPos}_Address, {PlayerPrefs.GetString($"IpCamera_{cameraPos}_Address", String.Empty)}");
            return PlayerPrefs.GetString($"IpCamera_{cameraPos}_Address", String.Empty);
        }

        public void Set_IpCameraMacAddress(CameraPos cameraPos, string address)
        {
            Log.d($"Set IpCamera_{cameraPos}_MacAddress, {address}");
            PlayerPrefs.SetString($"IpCamera_{cameraPos}_MacAddress", address);
        }

        public string Get_IpCameraMacAddress(CameraPos cameraPos)
        {
            Log.d($"Get IpCamera_{cameraPos}_MacAddress, {PlayerPrefs.GetString($"IpCamera_{cameraPos}_MacAddress", String.Empty)}");
            return PlayerPrefs.GetString($"IpCamera_{cameraPos}_MacAddress", String.Empty);
        }

        public bool IsHaveMac(CameraPos cameraPos)
        {
            return PlayerPrefs.HasKey($"IpCamera_{cameraPos}_MacAddress");
        }

        /// <summary>
        /// 保存USB相机MAC地址
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <param name="macAddress">MAC地址</param>
        public void Set_USBCameraMacAddress(int cameraIndex, string macAddress)
        {
            PlayerPrefs.SetString($"USBCamera_{cameraIndex}_MacAddress", macAddress);
            Debug.Log($"已保存USB相机{cameraIndex}的MAC地址: {macAddress}");
        }
        
        /// <summary>
        /// 获取USB相机MAC地址
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <returns>MAC地址，如果不存在则返回空字符串</returns>
        public string Get_USBCameraMacAddress(int cameraIndex)
        {
            return PlayerPrefs.GetString($"USBCamera_{cameraIndex}_MacAddress", string.Empty);
        }
        
        /// <summary>
        /// 检查是否存在USB相机MAC地址
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        /// <returns>如果存在返回true，否则返回false</returns>
        public bool Has_USBCameraMacAddress(int cameraIndex)
        {
            return PlayerPrefs.HasKey($"USBCamera_{cameraIndex}_MacAddress");
        }
        
        /// <summary>
        /// 删除USB相机MAC地址
        /// </summary>
        /// <param name="cameraIndex">相机索引：1表示第一个相机，2表示第二个相机</param>
        public void Delete_USBCameraMacAddress(int cameraIndex)
        {
            if (PlayerPrefs.HasKey($"USBCamera_{cameraIndex}_MacAddress"))
            {
                PlayerPrefs.DeleteKey($"USBCamera_{cameraIndex}_MacAddress");
                Debug.Log($"已删除USB相机{cameraIndex}的MAC地址");
            }
        }

        public void Set_BoardBrand(DartBoard.E_BoardBrand boardBrand)
        {
            PlayerPrefs.SetString("BoardBrand", boardBrand.ToString());
        }

        public DartBoard.E_BoardBrand Get_BoardBrand()
        {
            var str = PlayerPrefs.GetString("BoardBrand", DartBoard.E_BoardBrand.grandarts.ToString());
            if (str.Contains("gran"))
            {
                return DartBoard.E_BoardBrand.grandarts;
            }
            return (DartBoard.E_BoardBrand)Enum.Parse(typeof(DartBoard.E_BoardBrand), str);
        }

        public void InitialSetting()
        {
            PlayerPrefs.DeleteKey("BoardBrand");
            PlayerPrefs.DeleteKey($"IpCamera_{CameraPos.Left}_Address");
            PlayerPrefs.DeleteKey($"IpCamera_{CameraPos.Right}_Address");
            PlayerPrefs.DeleteKey($"IpCamera_{CameraPos.Left}_MacAddress");
            PlayerPrefs.DeleteKey($"IpCamera_{CameraPos.Right}_MacAddress");
            Delete_USBCameraMacAddress(1);
            Delete_USBCameraMacAddress(2);
            for (int i = 0; i < 4; i++)
            {
                Delete_Dot(CameraPos.Left, CalibrationType.Auto, i);
            }
            for (int i = 0; i < 4; i++)
            {
                Delete_Dot(CameraPos.Right, CalibrationType.Auto, i);
            }
            PlayerPrefs.Save();
        }

        #endregion

        #region 镖尖标定相关

        public void Set_BoxSize(float value)
        {
            PlayerPrefs.SetFloat("BoxSize", value);
        }
        public static float Get_BoxSize()
        {
            return PlayerPrefs.GetFloat("BoxSize", 23f);
        }

        public void Set_Filter_Conf(float value)
        {
            PlayerPrefs.SetFloat("Filter_Conf", value);
        }

        public float Get_Filter_Conf()
        {
            return PlayerPrefs.GetFloat("Filter_Conf", 0.5f);
        }

        public void Set_Filter_IoU(float value)
        {
            PlayerPrefs.SetFloat("Filter_IoU", value);
        }
        public float Get_Filter_IoU()
        {
            return PlayerPrefs.GetFloat("Filter_IoU", 0.1f);
        }

        public void Set_DartPredictModel(string modelName)
        {
            PlayerPrefs.SetString("IpCameraLeft_PredictorModel", modelName);
            Log.d($"IpCameraLeft_PredictorModelIndex, {modelName}");
        }

        public string Get_DartPredictModel()
        {
            var value = PlayerPrefs.GetString("IpCameraLeft_PredictorModel", "960s");
            Log.d($"Get IpCameraLeft_PredictorModel, {value}");
            if (!value.Equals("960n.tflite") && !value.Equals("960s.tflite") && !value.Equals("640n.tflite"))
            {
                Log.d($"Current model {value} is not supported, set to default 960s.tflite");
                PlayerPrefs.SetString($"IpCameraLeft_PredictorModel", "960s.tflite");
                return "960s.tflite";
            }
            return value;
        }

        public void Set_DartsPredict_Conf(float value)
        {
            PlayerPrefs.SetFloat("DartsPredict_Conf", value);
        }

        public float Get_DartsPredict_Conf()
        {
            return PlayerPrefs.GetFloat("DartsPredict_Conf", 0.25f);
        }

        public void Set_DartsPredict_IoU(float value)
        {
            PlayerPrefs.SetFloat("DartsPredict_IoU", value);
        }

        public float Get_DartsPredict_IoU()
        {
            return PlayerPrefs.GetFloat("DartsPredict_IoU", 0.7f);
        }

        #endregion

        #region 镖身识别相关
        public string Get_DartsCountModel()
        {
            return PlayerPrefs.GetString($"DartsCountPredictorModel", "960DartsCount_low.tflite");
        }

        public void Set_DartsCountModel(string modelName)
        {
            PlayerPrefs.SetString($"DartsCountPredictorModel", modelName);
        }

        #endregion

        public void Set_DartPredictorBestAccelerator(string modelName, string acceleratorType)
        {
            PlayerPrefs.SetString(modelName + "_acceleratorType", acceleratorType);
            Log.d($"Set DartPredictorModel: {modelName} BestAccelerator: {acceleratorType}");
        }

        public TfLiteDelegateType Get_DartPredictorBestAccelerator(string modelName)
        {
            var str = PlayerPrefs.GetString(modelName + "_acceleratorType", "XNNPACK");
            Log.d($"Get {modelName}: {str}");
            return (TfLiteDelegateType)Enum.Parse(typeof(TfLiteDelegateType), str);
        }

        public void Set_DartPredictSpeedLevel(string modelName, string speed)
        {
            PlayerPrefs.SetString(modelName + "_InferenceSpeed", speed);
            Log.d($"Set DartPredictorModel: {modelName} Speed: {speed}");
        }

        public string Get_DartPredictorSpeedLevel(string modelName)
        {
            var str = PlayerPrefs.GetString(modelName + "_InferenceSpeed", "Fast");
            Log.d($"Get {modelName}: {str}");
            return str;
        }

        public void Set_ModelDelegateType(string DelegateType)
        {
            PlayerPrefs.SetString("DelegateType", DelegateType);
            Log.d($"Set DelegateType: {DelegateType}");
        }

        public string Get_ModelDelegateType()
        {
            var str = PlayerPrefs.GetString("DelegateType", "GPU");
            Log.d($"Get DelegateType: {str}");
            return str;
        }

        // 存储CoreML飞镖尖模型名
        public void Set_CoreMLDartTipModel(string modelName)
        {
            PlayerPrefs.SetString("CoreML_DartTip_Model", modelName);
            Log.d($"Set CoreML_DartTip_Model: {modelName}");
        }

        // 获取CoreML飞镖尖模型名
        public string Get_CoreMLDartTipModel()
        {
            var value = PlayerPrefs.GetString("CoreML_DartTip_Model", "new_960s");
            Log.d($"Get CoreML_DartTip_Model: {value}");
            // 可选：校验合法性
            if (value != "new_960s" && value != "960n" && value != "640n")
            {
                Log.d($"Current CoreML_DartTip_Model {value} is not supported, set to default new_960s");
                PlayerPrefs.SetString("CoreML_DartTip_Model", "new_960s");
                return "new_960s";
            }
            return value;
        }

        #region 自定义飞镖板配置相关

        /// <summary>
        /// 【CH】保存自定义飞镖板配置
        /// 【EN】Save custom dartboard configuration
        /// 【JP】カスタムダーツボード設定を保存
        /// </summary>
        public void Set_CustomBoardConfig(CustomBoardConfig config)
        {
            if (config != null)
            {
                PlayerPrefs.SetFloat("CustomBoard_DoubleRadius", config.DoubleRadius);
                PlayerPrefs.SetFloat("CustomBoard_TripleRadius", config.TripleRadius);
                PlayerPrefs.SetFloat("CustomBoard_SingleInRadius", config.SingleInRadius);
                PlayerPrefs.SetFloat("CustomBoard_OuterBullRadius", config.OuterBullRadius);
                PlayerPrefs.SetFloat("CustomBoard_InnerBullRadius", config.InnerBullRadius);
                PlayerPrefs.Save();
                Log.d($"保存自定义飞镖板配置: Double={config.DoubleRadius}, Triple={config.TripleRadius}, SingleIn={config.SingleInRadius}, OuterBull={config.OuterBullRadius}, InnerBull={config.InnerBullRadius}");
            }
        }

        /// <summary>
        /// 【CH】获取自定义飞镖板配置
        /// 【EN】Get custom dartboard configuration
        /// 【JP】カスタムダーツボード設定を取得
        /// </summary>
        public CustomBoardConfig Get_CustomBoardConfig()
        {
            var config = new CustomBoardConfig
            {
                DoubleRadius = PlayerPrefs.GetFloat("CustomBoard_DoubleRadius", 170.5f),
                TripleRadius = PlayerPrefs.GetFloat("CustomBoard_TripleRadius", 106.5f),
                SingleInRadius = PlayerPrefs.GetFloat("CustomBoard_SingleInRadius", 96.5f),
                OuterBullRadius = PlayerPrefs.GetFloat("CustomBoard_OuterBullRadius", 17f),
                InnerBullRadius = PlayerPrefs.GetFloat("CustomBoard_InnerBullRadius", 7f)
            };

            Log.d($"加载自定义飞镖板配置: Double={config.DoubleRadius}, Triple={config.TripleRadius}, SingleIn={config.SingleInRadius}, OuterBull={config.OuterBullRadius}, InnerBull={config.InnerBullRadius}");
            return config;
        }

        /// <summary>
        /// 【CH】重置自定义飞镖板配置为默认值
        /// 【EN】Reset custom dartboard configuration to default
        /// 【JP】カスタムダーツボード設定をデフォルトにリセット
        /// </summary>
        public void Reset_CustomBoardConfig()
        {
            PlayerPrefs.DeleteKey("CustomBoard_DoubleRadius");
            PlayerPrefs.DeleteKey("CustomBoard_TripleRadius");
            PlayerPrefs.DeleteKey("CustomBoard_SingleInRadius");
            PlayerPrefs.DeleteKey("CustomBoard_OuterBullRadius");
            PlayerPrefs.DeleteKey("CustomBoard_InnerBullRadius");
            PlayerPrefs.Save();
            Log.d("重置自定义飞镖板配置为默认值");
        }

        #endregion

        public void Set_CountingMethod(string method)
        {
            PlayerPrefs.SetString("CountingMethod", method);
            Log.d($"Set CountingMethod: {method}");
        }

        public string Get_CountingMethod()
        {
            var str = PlayerPrefs.GetString("CountingMethod", "CountModel");
            Log.d($"Get CountingMethod: {str}");
            return str;
        }
    }
}