using System.Threading.Tasks;
using TensorFlowLite;
using UnityEngine;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        public enum CameraPos
        {
            /// <summary>
            /// Target14
            /// </summary>
            Left = 0,
            /// <summary>
            /// Target13
            /// </summary>
            Right = 1
        }

        private int m_DartModelSize = 640;

        private TfLiteDelegateType m_Accelerator = TfLiteDelegateType.XNNPACK;
        public async Task UpdateAccelerator2Model(TfLiteDelegateType accelerator)
        {
            await UpdateDartDetectorAcceleratorType(accelerator);
            await UpdateDartCountAcceleratorType(accelerator);
            await UpdateDartZoomAcceleratorType(TfLiteDelegateType.XNNPACK);
            Set_AcceleratorType(accelerator.ToString());
            m_Accelerator = accelerator;
        }
        public async Task Init(Transform targetTransform)
        {
            InitPredictParamer();
            InitCameras(targetTransform);
            await InitAIPredictors(targetTransform);
        }

        public struct ZoomResult
        {
            public readonly Texture2D texture;
            public readonly float overflowX;
            public readonly float overflowY;

            public ZoomResult(Texture2D texture, float overflowX, float overflowY)
            {
                this.texture = texture;
                this.overflowX = overflowX;
                this.overflowY = overflowY;
            }
        }
        
        public ZoomResult GetZoomedTexture(int camIndex, Texture sourceTexture, Vector2 centerPoint, float targetSize = 320f)
        {
            if (sourceTexture == null) return new ZoomResult(null, 0, 0);

            var realPos = GetCalibrationTool((CameraPos)camIndex).GetRealPointFromVirtual(centerPoint);
            var newRealPos = TflitePositionHelper.ConvertToPixelCoordinates(realPos, sourceTexture.width);

            var centerX = newRealPos.x + sourceTexture.width / 2.0f;
            var centerY = newRealPos.y + sourceTexture.height / 2.0f;

            var halfSize = targetSize / 2.0f;

            // 记录原始位置
            var originalSrcX = centerX - halfSize;
            var originalSrcY = centerY - halfSize;

            // 计算源区域的起始位置并处理边界
            var srcX = Mathf.Clamp(centerX - halfSize, 0, sourceTexture.width - targetSize);
            var srcY = Mathf.Clamp(centerY - halfSize, 0, sourceTexture.height - targetSize);

            // 计算超出边界的值
            var overflowX = originalSrcX - srcX; // 记录调整的值
            var overflowY = originalSrcY - srcY; // 记录调整的值

            // 创建目标纹理
            var zoomedTexture = new Texture2D((int)targetSize, (int)targetSize, TextureFormat.RGBA32, false);
            zoomedTexture.filterMode = FilterMode.Point;

            // 创建临时RenderTexture用于Graphics.Blit操作
            var tempRT = RenderTexture.GetTemporary(sourceTexture.width, sourceTexture.height, 0);
            Graphics.Blit(sourceTexture, tempRT);

            // 读取像素
            RenderTexture.active = tempRT;
            zoomedTexture.ReadPixels(new Rect(srcX, srcY, targetSize, targetSize), 0, 0);
            zoomedTexture.Apply();

            // 恢复之前的激活RenderTexture
            RenderTexture.active = null;
            RenderTexture.ReleaseTemporary(tempRT);

            return new ZoomResult(zoomedTexture, overflowX, overflowY); // 返回结构体实例
        }
    }
}