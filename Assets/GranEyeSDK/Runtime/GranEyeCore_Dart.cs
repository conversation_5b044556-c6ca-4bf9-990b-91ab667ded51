using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using TensorFlowLite;
using UnityEngine;
using com.luxza.granlog;
using System.Collections;
using Cysharp.Threading.Tasks;

namespace com.luxza.graneye
{
    public partial class GranEyeCore
    {
        #region 镖尖模型相关

        // AI预测结果列表
        private List<CompareFilter.PredictWithBox> m_HistoryResults = new();

        private List<CompareFilter.PredictWithBox>
            m_PredictResultPointEntityList = new();

        private readonly List<CompareFilter.PredictWithBox>
            m_PredictHistoryResults = new();

        private List<Vector3> m_Top30Results = new();
        private readonly Dictionary<CameraPos, List<Vector3>> m_CameraTop30Results = new();
        private readonly List<CompareFilter.PredictWithBox> m_IOU_Results = new();
        private Dictionary<int, List<double[]>> m_DBScan_Results = new();
        private Dictionary<int, List<double[]>> m_KMeans_Results = new();
        private Dictionary<int, List<double[]>> m_DBScan_Results_For_Compare = new();
        private readonly List<double[]> m_RevisedResults = new();
        private int m_PredictResultDartCountList;
        private Dictionary<int, List<double[]>> m_SortedHistoryResults = new();
        private readonly List<DartPredictor> m_DartDetectors = new();
        private DartZoomPredictor m_DartZoomPredictors = new();
        private readonly List<DartBodyPredictor> m_DartCountPredictors = new();

        private double m_DetectionTakesTime;
        private DateTime m_DetectionTakesStartTime;
        private DateTime m_DetectionTakesEndTime;
        private double m_InferenceTakesTime;
        public bool IsBusy { get; private set; } = false;
        private bool historyIsFixed = false;

        private float m_BoxSize;
        private float m_Filter_Conf;
        private float m_Filter_IoU;

        public enum PredictErrorCode
        {
            None,
            LeftCameraPredictFailed,
            RightCameraPredictFailed,
            AllCamerasPredictFailed
        }

        private void InitPredictParamer()
        {
            m_BoxSize = Get_BoxSize();
            m_Filter_Conf = Get_Filter_Conf();
            m_Filter_IoU = Get_Filter_IoU();
            var str = PlayerPrefs.GetString("AcceleratorType", "XNNPACK");
            m_Accelerator = (TfLiteDelegateType)Enum.Parse(typeof(TfLiteDelegateType), str);
        }

        public async Task UpdateModel(string modelName, float conf, float iou)
        {
            m_Accelerator = TfLiteDelegateType.XNNPACK;
            Set_AcceleratorType(m_Accelerator.ToString());
            m_DartModelSize = modelName.Contains("960") ? 960 : 640;
            for (var i = 0; i < m_DartDetectors.Count; i++)
            {
                var dartDetector = m_DartDetectors[i];
                await dartDetector.Init(modelName, m_Accelerator, m_DartModelSize, conf, iou);
            }
            await WarmupModels().ConfigureAwait(false);
            Log.d($"UpdateModel: {modelName}");
        }
        private async Task UpdateDartDetectorAcceleratorType(TfLiteDelegateType accelerator)
        {
            var modelName = Get_DartPredictModel();
            m_DartModelSize = modelName.Contains("960") ? 960 : 640;
            Log.d($"{modelName} {m_DartModelSize} {accelerator}");
            await m_DartDetectors[0].Init(modelName, accelerator, m_DartModelSize);
            await m_DartDetectors[1].Init(modelName, accelerator, m_DartModelSize);
            Log.d($"UpdateModel: {modelName}");
        }

        public async Task UpdateAllModelAcceleratorType(string modelName)
        {
            var AcceleratorType = Get_DartPredictorBestAccelerator(modelName);
            await UpdateDartDetectorAcceleratorType(AcceleratorType);
            await UpdateDartCountAcceleratorType(AcceleratorType);
            Set_AcceleratorType(AcceleratorType.ToString());
            await WarmupModels().ConfigureAwait(false);
        }

        private async Task UpdateDartCountAcceleratorType(TfLiteDelegateType accelerator)
        {
            var modelName = Get_DartsCountModel();
            for (var i = 0; i < m_DartCountPredictors.Count; i++)
            {
                await m_DartCountPredictors[i].Init(modelName, accelerator);
            }
        }

        private async Task UpdateDartZoomAcceleratorType(TfLiteDelegateType accelerator)
        {
            var modelName = "DartZoom.tflite";
            await m_DartZoomPredictors.Init(modelName, accelerator);
        }

        /// <summary>
        /// 修改IOU阈值
        /// </summary>
        /// <param name="iou"></param>
        public void OnFilterIoUChanged(string iou)
        {
            if (float.TryParse(iou, out var iouValue))
            {
                m_Filter_IoU = iouValue;
                Set_Filter_IoU(iouValue);
            }
        }

        /// <summary>
        /// 修改置信度
        /// </summary>
        public void OnFilterConfChanged(string conf)
        {
            if (float.TryParse(conf, out var confValue))
            {
                m_Filter_Conf = confValue;
                Set_Filter_Conf(confValue);
            }
        }
        /// <summary>
        /// 修改边框尺寸
        /// </summary>
        /// <param name="size"></param>
        public void OnBoxSizeChanged(string size)
        {
            if (float.TryParse(size, out var sizeValue))
            {
                m_BoxSize = sizeValue;
                Set_BoxSize(sizeValue);
                foreach (var predictWithBox in m_PredictResultPointEntityList)
                {
                    predictWithBox.boxSize = m_BoxSize;
                    predictWithBox.UpdateBoxCoordinates();
                }
            }
        }

        /// <summary>
        /// 获取30个预测结果
        /// </summary>
        /// <returns></returns>
        public List<Vector3> GetTop30Results()
        {
            return m_Top30Results;
        }
        /// <summary>
        /// 获取左右相机的30个预测结果
        /// </summary>
        /// <returns></returns>
        public Dictionary<CameraPos, List<Vector3>> GetCameraTop30Results()
        {
            return m_CameraTop30Results;
        }

        /// <summary>
        /// 获取历史预测结果列表
        /// </summary>
        /// <returns></returns>
        public List<CompareFilter.PredictWithBox> GetHistoryResults()
        {
            return m_HistoryResults;
        }

        public List<CompareFilter.PredictWithBox> GetPredictResultPointEntityList()
        {
            return m_PredictResultPointEntityList;
        }

        /// <summary>
        /// 获取当前IOU过滤的结果
        /// </summary>
        /// <returns>返回包含IOU过滤结果的PredictWithBox列表</returns>
        public List<CompareFilter.PredictWithBox> GetIOUResults()
        {
            return m_IOU_Results;
        }

        /// <summary>
        /// 获取当前DBScan聚类的结果
        /// </summary>
        /// <returns>返回包含DBScan聚类结果的字典，其中键为聚类ID，值为该聚类中的坐标点列表</returns>
        public Dictionary<int, List<double[]>> GetDBScanResults()
        {
            return m_DBScan_Results;
        }

        /// <summary>
        /// 获取当前KMeans聚类的结果
        /// </summary>
        /// <returns>返回包含KMeans聚类结果的字典，其中键为聚类ID，值为该聚类中的坐标点列表</returns>
        public Dictionary<int, List<double[]>> GetKMeansResults()
        {
            return m_KMeans_Results;
        }

        /// <summary>
        /// 获取经过修正后的结果
        /// </summary>
        /// <returns>返回修正后的坐标点列表，每个坐标点为double数组表示</returns>
        public List<double[]> GetRevisedResults()
        {
            return m_RevisedResults;
        }

        /// <summary>
        /// 获取预测的镖数计数列表
        /// </summary>
        /// <returns>返回预测检测到的镖数量列表</returns>
        public int GetPredictResultDartCounts()
        {
            return m_PredictResultDartCountList;
        }
        /// <summary>
        /// 获取推理结果时间
        /// </summary>
        /// <returns></returns>
        public double GetDetectionTakesTime()
        {
            return m_DetectionTakesTime;
        }
        /// <summary>
        /// 获取推理时间
        /// </summary>
        /// <returns></returns>
        public double GetInferenceTakesTime()
        {
            return m_InferenceTakesTime;
        }

        public void CleanHistoryData()
        {
            this.m_HistoryResults.Clear();
            RecoveryService.ClearAll();

            this.m_PredictResultPointEntityList.Clear();
            this.m_IOU_Results.Clear();
            this.m_DBScan_Results.Clear();
            this.m_KMeans_Results.Clear();
            this.m_Top30Results.Clear();
            this.m_CameraTop30Results.Clear();
            this.m_PredictHistoryResults.Clear();
            Log.d("All HistoryData Cleared");
        }

        /// <summary>
        /// Delete data from the specified index and onwards
        /// </summary>
        /// <param name="index"></param>
        public void CleanHistoryDataByIndex(int index)
        {
            if (index < m_HistoryResults.Count)
            {
                m_HistoryResults.RemoveRange(index, m_HistoryResults.Count - index);
            }
        }

        private async Task InitAIPredictors(Transform targetTransform)
        {
            var modelName = Get_DartPredictModel();
            m_DartModelSize = modelName.Contains("960") ? 960 : 640;
            var tLeft = new GameObject("DartPredictor-Left").AddComponent<DartPredictor>();
            if (tLeft == null)
            {
                Log.e($"tLeft is null");
            }
            var t_StartTime = DateTime.Now;
            await tLeft.Init(modelName, m_Accelerator, m_DartModelSize, Get_DartsPredict_Conf(),
                Get_DartsPredict_IoU());
            Log.d($"LeftDartModel.Init took {(DateTime.Now - t_StartTime).TotalMilliseconds} ms");
            this.m_DartDetectors.Add(tLeft);
            tLeft.transform.SetParent(targetTransform);

            var tRight = new GameObject("DartPredictor-Right").AddComponent<DartPredictor>();
            if (tRight == null)
            {
                Log.e($"tRight is null");
            }
            t_StartTime = DateTime.Now;
            await tRight.Init(modelName, m_Accelerator, m_DartModelSize,
                Get_DartsPredict_Conf(), Get_DartsPredict_IoU());

            Log.d($"RightDartModel.Init took {(DateTime.Now - t_StartTime).TotalMilliseconds} ms");
            this.m_DartDetectors.Add(tRight);
            tRight.transform.SetParent(targetTransform);

            var dartCountModelName = Get_DartsCountModel();
            // 左边预测器
            var tDartCountLeft = new GameObject("DartCountPredictor-Left").AddComponent<DartBodyPredictor>();
            t_StartTime = DateTime.Now;
            await tDartCountLeft.Init(dartCountModelName, m_Accelerator);
     
            Log.d($"LeftDartCountModel.Init took {(DateTime.Now - t_StartTime).TotalMilliseconds} ms");
            m_DartCountPredictors.Add(tDartCountLeft);
            tDartCountLeft.transform.SetParent(targetTransform);

            // 右边预测器
            var tDartCountRight = new GameObject("DartCountPredictor-Right").AddComponent<DartBodyPredictor>();
            t_StartTime = DateTime.Now;
            await tDartCountRight.Init(dartCountModelName, m_Accelerator);
            Log.d($"RightDartCountModel.Init took {(DateTime.Now - t_StartTime).TotalMilliseconds} ms");
            m_DartCountPredictors.Add(tDartCountRight);
            tDartCountRight.transform.SetParent(targetTransform);

            // 初始化DartZoomPredictor
            // var DartZoomStartTime = DateTime.Now;
            // InitDartZoomPredictor();
            // var DartZoomEndTime = DateTime.Now;
            // Log.d($"DartZoomModel.Init took {(DartZoomEndTime - DartZoomStartTime).TotalMilliseconds} ms");

            // 模型预热
            // await WarmupModels().ConfigureAwait(false);
        }

        private DartPredictor GetDartPredictor(CameraPos pos)
        {
            if ((int)pos > this.m_DartDetectors.Count - 1 || (int)pos < 0)
                throw new System.Exception("index out of range");
            return this.m_DartDetectors[(int)pos];
        }

        private DartBodyPredictor GetDartCountPredictor(CameraPos pos)
        {
            if ((int)pos > this.m_DartCountPredictors.Count - 1 || (int)pos < 0)
                throw new Exception("index out of range");
            return this.m_DartCountPredictors[(int)pos];
        }

        private DartZoomPredictor GetZoomPredictor()
        {
            return this.m_DartZoomPredictors;
        }

        private async Task WarmupModels()
        {
            try
            {
                Log.d("Starting model warmup...");
                var warmupTexture = Texture2D.blackTexture;

                // 对左右相机分别执行预热
                var t1 = DartPredict(CameraPos.Left, warmupTexture);
                var t2 = DartPredict(CameraPos.Right, warmupTexture);
                var t3 = DartCountPredict(CameraPos.Left, warmupTexture);
                var t4 = DartCountPredict(CameraPos.Right, warmupTexture);

                // Run all tasks in parallel and wait for them to complete
                await Task.WhenAll(t1, t2, t3, t4);

                Log.d("Model warmup completed");
            }
            catch (Exception e)
            {
                Log.w($"Model warmup failed, but it does not affect normal use: {e.Message}");
            }
        }

        private async Task WarmupWithTimeout<T>(Func<Task<T>> predictFunc, string modelName, TimeSpan timeout)
        {
            try
            {
                Log.d("Starting model warmup...");

                var warmupTexture = Texture2D.blackTexture;

                // 对左右相机分别执行预热
                await DartPredict(CameraPos.Left, warmupTexture);
                Log.d("Model warmup completed");
            }
            catch (Exception e)
            {
                Log.w($"{modelName} warmup encountered an error: {e.Message}");
            }
        }

        public async Task<(bool, List<CompareFilter.PredictWithBox>, PredictErrorCode)> Predict(int throwCount, bool runNMS = true, CancellationToken cancellationToken = default)
        {
            Log.d($"m_HistoryResults.Count: {m_HistoryResults.Count}");
            var t_InferenceTakesStartTime = DateTime.Now;
            this.IsBusy = true;
            // 清空之前的预测结果
            this.m_PredictResultPointEntityList.Clear();
            this.m_PredictResultDartCountList = 0;
            this.m_IOU_Results.Clear();
            this.m_DBScan_Results.Clear();
            this.m_KMeans_Results.Clear();
            m_Top30Results.Clear();
            m_CameraTop30Results.Clear();

            var t_LeftTask = Predict(CameraPos.Left, runNMS, cancellationToken);
            var t_RightTask = Predict(CameraPos.Right, runNMS, cancellationToken);
            var t = await Task.WhenAll(t_LeftTask, t_RightTask);

            if (m_HistoryResults.Count > 0)
            {
                m_PredictResultPointEntityList.AddRange(
                    m_HistoryResults
                        .Where(history => history.y != -1730) // yが-1730の場合はMissの仮データなので除外
                        .Select(history =>
                            new CompareFilter.PredictWithBox(history.x, history.y, 1.0f, m_BoxSize))
                );
                foreach (var box in m_HistoryResults)
                {
                    box.score = 1.0f;  // 只需要修改 score 为 1.0f
                }
            }

            this.Calculate30Pos(CameraPos.Left, t[0].predictResultList, ref m_PredictResultPointEntityList);
            this.Calculate30Pos(CameraPos.Right, t[1].predictResultList, ref m_PredictResultPointEntityList);

            var t_InferenceTakesEndTime = DateTime.Now;
            this.m_InferenceTakesTime = (t_InferenceTakesEndTime - t_InferenceTakesStartTime).TotalMilliseconds;

            m_DetectionTakesStartTime = DateTime.Now;
            m_PredictResultPointEntityList = m_PredictResultPointEntityList.OrderByDescending(x => x.score).ToList();
            m_Top30Results = m_PredictResultPointEntityList
               .Select(x => new Vector3(x.x, x.y, 0))
               .ToList();
            var sortedResultsCopy = m_PredictResultPointEntityList.ToList();
            this.ProcessByIOU(sortedResultsCopy);

            var data = await ProcessFinalResults(throwCount);
            var errorCode = PredictErrorCode.None;
            if (!t[0].isSuccess && !t[1].isSuccess)
            {
                errorCode = PredictErrorCode.AllCamerasPredictFailed;
            }
            else if (!t[0].isSuccess)
            {
                errorCode = PredictErrorCode.LeftCameraPredictFailed;
            }
            else if (!t[1].isSuccess)
            {
                errorCode = PredictErrorCode.RightCameraPredictFailed;
            }
            return (data.isFixed, data.predictList, errorCode);
        }

        private async Task<(bool, List<CompareFilter.PredictWithBox>, PredictErrorCode)> PredictWithTexture(int throwCount, Dictionary<CameraPos, Texture> textures, bool runNMS = true, CancellationToken cancellationToken = default)
        {
            Log.d($"m_HistoryResults.Count: {m_HistoryResults.Count}");
            var t_InferenceTakesStartTime = DateTime.Now;
            this.IsBusy = true;
            // 清空之前的预测结果
            this.m_PredictResultPointEntityList.Clear();
            this.m_PredictResultDartCountList = 0;
            this.m_IOU_Results.Clear();
            this.m_DBScan_Results.Clear();
            this.m_KMeans_Results.Clear();
            m_Top30Results.Clear();
            m_CameraTop30Results.Clear();

            var t_LeftTask = PredictWithStoredImage(CameraPos.Left, textures[CameraPos.Left], runNMS, cancellationToken);
            var t_RightTask = PredictWithStoredImage(CameraPos.Right, textures[CameraPos.Right], runNMS, cancellationToken);
            var t = await Task.WhenAll(t_LeftTask, t_RightTask);

            if (m_HistoryResults.Count > 0)
            {
                m_HistoryResults = m_HistoryResults
                    .Where(history => history.y != -1730) // yが-1730の場合はMissの仮データなので除外
                    .ToList();

                m_PredictResultPointEntityList.AddRange(
                    m_HistoryResults.Select(history =>
                        new CompareFilter.PredictWithBox(history.x, history.y, 1.0f, m_BoxSize))
                );

                foreach (var box in m_HistoryResults)
                {
                    box.score = 1.0f;  // 只需要修改 score 为 1.0f
                }
            }

            this.Calculate30Pos(CameraPos.Left, t[0].predictResultList, ref m_PredictResultPointEntityList);
            this.Calculate30Pos(CameraPos.Right, t[1].predictResultList, ref m_PredictResultPointEntityList);

            var t_InferenceTakesEndTime = DateTime.Now;
            this.m_InferenceTakesTime = (t_InferenceTakesEndTime - t_InferenceTakesStartTime).TotalMilliseconds;

            m_DetectionTakesStartTime = DateTime.Now;
            m_PredictResultPointEntityList = m_PredictResultPointEntityList.OrderByDescending(x => x.score).ToList();

            //各投数の結果を保存しておく
            if (throwCount != 1)
            {
                foreach (var item in m_PredictResultPointEntityList)
                {
                    m_PredictHistoryResults.Add(item);
                }
            }

            m_Top30Results = m_PredictResultPointEntityList
               .Select(x => new Vector3(x.x, x.y, 0))
               .ToList();
            var sortedResultsCopy = m_PredictResultPointEntityList.ToList();
            this.ProcessByIOU(sortedResultsCopy);

            var data = await ProcessFinalResultsWithTexture(throwCount, textures);
            var errorCode = PredictErrorCode.None;
            if (!t[0].isSuccess && !t[1].isSuccess)
            {
                errorCode = PredictErrorCode.AllCamerasPredictFailed;
            }
            else if (!t[0].isSuccess)
            {
                errorCode = PredictErrorCode.LeftCameraPredictFailed;
            }
            else if (!t[1].isSuccess)
            {
                errorCode = PredictErrorCode.RightCameraPredictFailed;
            }
            return (data.isFixed, data.predictList, errorCode);
        }

        private List<TFEntities.PredictResultPointEntity> GetMaxDartsCount(List<TFEntities.PredictResultPointEntity>[] dartsCount)
        {
            // 检查第一个相机的结果
            var tag0 = (dartsCount[0] == null || dartsCount[0].Count == 0) ? 0 : dartsCount[0][0].Tag;

            // 检查第二个相机的结果
            var tag1 = (dartsCount[1] == null || dartsCount[1].Count == 0) ? 0 : dartsCount[1][0].Tag;

            // 比较tag值并返回对应列表，如果Tag相等或都为0，优先返回非空列表
            if (tag0 > tag1)
            {
                return dartsCount[0];
            }
            else if (tag1 > tag0)
            {
                return dartsCount[1];
            }
            else
            {
                // Tag相等时，返回非空列表
                return (dartsCount[0] == null || dartsCount[0].Count == 0) ? dartsCount[1] : dartsCount[0];
            }
        }

        private void Calculate30Pos(CameraPos position, List<TFEntities.PredictResultPointEntity> pos, ref List<CompareFilter.PredictWithBox> recorder)
        {
            var cameraResults = new List<Vector3>();

            var t_FilterPos = pos
                .Where(item => item.score > m_Filter_Conf)
                .OrderByDescending(x => x.score)
                .ToArray();

            // 原中文日志：推理{position}的相机的数据经过置信度筛选后有{t_FilterPos.Length}个结果
            Log.d($"After confidence filtering, the {position} camera inference data has {t_FilterPos.Length} results");

            // 7. 处理预测结果
            foreach (var predictResult in t_FilterPos)
            {
                if (predictResult.score * 100 > 100.0f)
                {
                    // 原中文日志：发现异常置信度：{predictResult.score}，已忽略该结果
                    Log.w($"Abnormal confidence detected: {predictResult.score}, result ignored");
                    continue;
                }

                var realPosition = TflitePositionHelper.ModelPos2UnityPos(new Vector2(predictResult.x, predictResult.y));

                // 将真实坐标转换回虚拟坐标
                var virtualPosition = GetCalibrationTool(position).GetVirtualPointFromReal(new Vector3(realPosition.x, realPosition.y, 0));

                // 添加到相机结果列表
                cameraResults.Add(new Vector3(virtualPosition.x, virtualPosition.y, 0));

                // 添加到总预测结果列表
                recorder.Add(new CompareFilter.PredictWithBox(
                    virtualPosition.x, virtualPosition.y, predictResult.score, m_BoxSize));
            }

            // 保存该相机的Top30结果
            m_CameraTop30Results[position] = cameraResults;
        }

        private void ProcessByIOU(List<CompareFilter.PredictWithBox> sortedResults)
        {
            while (sortedResults.Count > 0)
            {
                m_IOU_Results.Add(sortedResults[0]);
                sortedResults.RemoveAt(0);
                sortedResults = sortedResults.Where(b =>
                {
                    if (b.score == 1.0f)
                    {
                        Log.d($"Score is 1.0f for box with coordinates: x={b.x}, y={b.y}");
                    }
                    return b.score == 1.0f || CompareFilter.ComputeIoU(m_IOU_Results[^1], b) < m_Filter_IoU;
                }).ToList();
            }
            // 原中文日志：开始使用IoU算法过滤数据。总数据为{m_IOU_Results.Count}
            Log.d($"Start filtering data using the IoU algorithm. The total data is {m_IOU_Results.Count}");
        }

        private void ProcessByDBScan(List<CompareFilter.PredictWithBox> sortedResults, int dartCount)
        {
            // DBSCAN算法过滤
            var clusteredData = CompareFilter.DBSCAN(
                sortedResults.Select(item => new double[] { item.x, item.y }).ToList(),
                20f, 3);

            if (clusteredData.Count > dartCount)
            {
                var maxCluster = clusteredData.Values.OrderByDescending(c => c.Count);
                var t_Cluster = maxCluster.Take(dartCount).ToList();
                m_DBScan_Results.Clear();

                for (var i = 0; i < t_Cluster.Count; i++)
                {
                    m_DBScan_Results.Add(i + 1, t_Cluster[i]);
                }
            }
            else
            {
                m_DBScan_Results = clusteredData;
            }
            // 原中文日志：经过dbscan算法过滤后, 数据量一共有{m_DBScan_Results.Count}个
            Log.d($"After DBSCAN algorithm filtering, there are a total of {m_DBScan_Results.Count} data points");
        }

        private void ProcessByKmeans(List<CompareFilter.PredictWithBox> sortedResults, int dartCount)
        {
            if (sortedResults.Count > 0)
            {
                //K-Means 聚类
                var t_KMeans = CompareFilter.KMeans(
                    sortedResults.Select(item => new double[] { item.x, item.y }).ToList(),
                    dartCount);
                m_KMeans_Results.Clear();
                if (t_KMeans.Count != 0)
                {
                    for (int i = 1; i <= dartCount; i++)
                    {
                        m_KMeans_Results.Add(i, t_KMeans[i - 1]);
                    }
                }
            }
            else
            {
                m_KMeans_Results = new Dictionary<int, List<double[]>>();
            }
            // 原中文日志：经过kmeans算法过滤后, 数据量一共有{m_KMeans_Results.Count}个
            Log.d($"After K-means algorithm filtering, there are a total of {m_KMeans_Results.Count} data points");
        }

        private void ProcessByDBScanForCompare(List<CompareFilter.PredictWithBox> sortedResults, int dartCount)
        {
            var clusteredData = CompareFilter.DBSCAN(
                sortedResults.Select(item => new double[] { item.x, item.y }).ToList(),
                20f, 3);

            if (clusteredData.Count > dartCount)
            {
                var maxCluster = clusteredData.Values.OrderByDescending(c => c.Count);
                var t_Cluster = maxCluster.Take(dartCount).ToList();
                m_DBScan_Results_For_Compare.Clear();

                for (var i = 0; i < t_Cluster.Count; i++)
                {
                    m_DBScan_Results_For_Compare.Add(i + 1, t_Cluster[i]);
                }
            }
            else
            {
                m_DBScan_Results_For_Compare = clusteredData;
            }
            Log.d($"Kmeans Groups for Comparison, Count:{m_DBScan_Results_For_Compare.Count}");
        }

        private List<List<double[]>> FinalProcessByKmeans(List<double[]> sortedResults)
        {
            if (sortedResults.Count > 0)
            {
                var t_KMeans = CompareFilter.KMeans(sortedResults, 2);
                return t_KMeans;
            }
            else
            {
                return new List<List<double[]>>();
            }
        }

        private Vector3 filteredPoint = Vector3.zero;

        private float GetScoreByPosition(double[] pos)
        {
            var predict = m_PredictResultPointEntityList.FirstOrDefault(p => p.x == pos[0] && p.y == pos[1]);
            if (predict != null) return predict.score;
            // 原中文日志：误反应！！没有推理出任何点 [{pos[0]}, {pos[1]}]
            Log.e($"False reaction!! No points inferred at [{pos[0]}, {pos[1]}]");
            return 0f;
        }

        private float GetScoreByPositionNoError(double[] pos)
        {
            var predict = m_PredictResultPointEntityList.FirstOrDefault(p => p.x == pos[0] && p.y == pos[1]);
            if (predict != null) return predict.score;
            return 0f;
        }

        private async Task<(bool isFixed, List<CompareFilter.PredictWithBox> predictList)> ProcessFinalResultsWithTexture(int throwCount, Dictionary<CameraPos, Texture> textures)
        {
            //以下情况是iou算出来的数据和dartCount不匹配的计算
            //如果iou的结果数量多于dartCount，就去获取dbscan的结果
            m_RevisedResults.Clear();

            var t_Fixed = false;
            historyIsFixed = false;
            if (m_IOU_Results.Count != throwCount)
            {
                t_Fixed = true;
                // 原中文日志：iou算出来的数据一共有{m_IOU_Results.Count}个和throwCount:{throwCount}不匹配
                Log.d($"IoU filtering resulted in {m_IOU_Results.Count} data points, which doesn't match throwCount: {throwCount}");

                if (Get_CountingMethod() == "CountModel")
                {
                    // 并行执行左右相机的DartCountPredict
                    var dartCountTasks = await Task.WhenAll(
                    DartCountPredict(CameraPos.Left, textures[CameraPos.Left]),
                    DartCountPredict(CameraPos.Right, textures[CameraPos.Right])
                    );

                    // 使用GetMaxDartsCount获取最终结果
                    var result = GetMaxDartsCount(dartCountTasks);
                    m_PredictResultDartCountList = result.Count > 0 ? result[0].Tag : 0;
                    // 原中文日志：镖数为: {m_PredictResultDartCountList}
                    Log.d($"Dart count: {m_PredictResultDartCountList}");
                }
                else
                {
                    m_PredictResultDartCountList = 4;// Provisional value that will not be adopted
                }

                //カウントモデルの数がエコーのカウントより多い場合に、エコーのカウントを使う
                var FinalCountResult = m_PredictResultDartCountList > throwCount ? throwCount : m_PredictResultDartCountList;

                // 执行聚类分析
                ProcessClustering(m_PredictResultPointEntityList, FinalCountResult);

                //如果dbscan的结果数量等于dartCount，那么我们就过滤一下每一个群,保留执行度最高的点
                if (m_DBScan_Results.Count == FinalCountResult)
                {
                    foreach (var item in m_DBScan_Results)
                    {
                        var t = item.Value;
                        try
                        {
                            t.Sort((x, y) => GetScoreByPosition(y).CompareTo(GetScoreByPosition(x)));
                            if (t.Count > 0)
                            {
                                m_RevisedResults.Add(t[0]);
                            }
                            else
                            {
                                // 原中文日志：m_ClusterResults排序后列表为空，无法添加元素
                                Log.e("m_ClusterResults list is empty after sorting, cannot add elements");
                            }
                        }
                        catch (Exception e)
                        {
                            // 原中文日志：m_ClusterResults排序或获取时发生错误
                            Log.e($"Error during sorting or accessing m_ClusterResults: {e.Message}");
                        }
                    }
                }
                else
                {
                    {
                        if (m_DBScan_Results.Count == 2)
                        {
                            CheckHistoryTwoCount();
                            ProcessClusteringByKmeansWithDBScan(throwCount);
                        }
                        if (!historyIsFixed)
                        {
                            //用k-means的结果来过滤
                            foreach (var item in m_KMeans_Results)
                            {
                                var t = item.Value;
                                try
                                {
                                    t.Sort((x, y) => GetScoreByPosition(y).CompareTo(GetScoreByPosition(x)));
                                    if (t.Count > 0)
                                    {
                                        m_RevisedResults.Add(t[0]);
                                    }
                                    else
                                    {
                                        // 原中文日志：m_KMeansResults排序后列表为空，无法添加元素
                                        Log.e("m_KMeansResults list is empty after sorting, cannot add elements");
                                    }
                                }
                                catch (Exception e)
                                {
                                    // 原中文日志：m_KMeansResults排序或获取时发生错误
                                    Log.e($"Error during sorting or accessing m_KMeansResults: {e.Message}");
                                }
                            }
                        }
                    }
                }

                // 处理历史结果和新预测结果的区域比较，并保留不重复的结果
                var predictionZones = new List<(string Key, CompareFilter.PredictWithBox Value)>();

                foreach (var point in m_RevisedResults)
                {
                    filteredPoint.x = (float)point[0];
                    filteredPoint.y = (float)point[1];
                    var zone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                    var score = GetScoreByPosition(point);
                    predictionZones.Add((zone,
                        new CompareFilter.PredictWithBox(filteredPoint.x, filteredPoint.y, score, m_BoxSize)));
                    // 原中文日志：更正之后的区域: {zone}, 坐标为:x:{(float)point[0]},y:{(float)point[1]}, 置信度:{score}
                    Log.d($"Corrected zone: {zone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, confidence: {score}");
                }

                // 移除重复的预测结果
                var uniquePredictions = new List<(string Key, CompareFilter.PredictWithBox Value)>();
                Log.d($"predictionZones.Count：{predictionZones.Count}");
                // 检查predictionZones中键的数量

                // 提取score不等于1的项
                var t_FinalPredictionZones = predictionZones
                    .Where(prediction => prediction.Value.score != 1.0f)
                    .ToList();

                uniquePredictions.AddRange(t_FinalPredictionZones);

                    // 原中文日志：提取score不等于1的结果，共有{uniquePredictions.Count}个
                Log.d($"Extracted results with score not equal to 1, total: {uniquePredictions.Count}");

                // 更新历史结果
                if (uniquePredictions.Count == 0)
                {
                    // 原中文日志：没有可排除区域，更新历史记录
                    Log.d("No excludable zones, updating history record");
                    foreach (var box in m_HistoryResults)
                    {
                        box.score = 1.0f;  // 只需要修改 score 为 1.0f
                    }
                }
                else
                {
                    foreach (var (_, box) in uniquePredictions)
                    {
                        filteredPoint.x = box.x;
                        filteredPoint.y = box.y;
                        var dartZone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                        // 原中文日志：剩余后的区域: {dartZone}，坐标为x：{filteredPoint.x},y:{filteredPoint.y},添加到历史记录中, 置信度为:{box.score}
                        Log.d($"Remaining zone: {dartZone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, added to history record, confidence: {box.score}");
                        box.dartZone = dartZone;
                        m_HistoryResults.Add(box);
                        RecoveryService.Save(box);
                    }
                }
            }
            else
            {
                // 清空历史结果
                m_HistoryResults.Clear();
                RecoveryService.ClearAll();

                // 处理最终结果
                foreach (var box in m_IOU_Results)
                {
                    filteredPoint.x = box.x;
                    filteredPoint.y = box.y;
                    // 获取dartZone并设置
                    var dartZone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                    box.dartZone = dartZone;
                    // 原中文日志：定位出打镖数据为:{dartZone}, 坐标为x：{filteredPoint.x},y:{filteredPoint.y},置信度为:{box.score}
                    Log.d($"Located dart data: {dartZone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, confidence: {box.score}");
                    m_HistoryResults.Add(box);
                    RecoveryService.Save(box);
                }

                // 原中文日志：经过IoU算法过滤后, 数据量一共有{m_HistoryResults.Count}个
                Log.d($"After IoU algorithm filtering, there are a total of {m_HistoryResults.Count} data points");
            }

            this.IsBusy = false;

            m_DetectionTakesEndTime = DateTime.Now;
            m_DetectionTakesTime = (m_DetectionTakesEndTime - m_DetectionTakesStartTime).TotalMilliseconds;

            // 在返回结果前，检查m_HistoryResults的数量，如果少于throwCount，则添加缺失的飞镖记录
            if (m_HistoryResults.Count < throwCount)
            {
                int missingDarts = throwCount - m_HistoryResults.Count;
                Log.d($"Adding {missingDarts} missing darts at floor position (x=0, y=-1730)");
                
                for (int i = 0; i < missingDarts; i++)
                {
                    var floorDart = new CompareFilter.PredictWithBox(0, -1730, 1f, m_BoxSize);
                    floorDart.dartZone = "Miss"; // 设置为Miss区域
                    m_HistoryResults.Add(floorDart);
                    RecoveryService.Save(floorDart);
                }
            }
            
            return (t_Fixed, m_HistoryResults.OrderByDescending(x => x.score).ToList());
        }

        private void CheckHistoryTwoCount()
        {
            var selectedResults = new List<CompareFilter.PredictWithBox>();
            bool hasGroupWithTwoScoreOne = false;

            // m_DBScan_Results の各クラスタをループ
            foreach (var cluster in m_DBScan_Results)
            {
                int scoreOneCount = 0;
                var scoreOnePoints = new List<CompareFilter.PredictWithBox>();

                // クラスタ内の各ポイントをループ
                foreach (var point in cluster.Value)
                {
                    // m_PredictResultPointEntityList から一致する予測結果を探す
                    var matchingPredict = m_PredictResultPointEntityList.FirstOrDefault(p => p.x == point[0] && p.y == point[1]);

                    // 一致する予測結果があり、スコアが1.0fの場合、カウントを増やす
                    if (matchingPredict != null && matchingPredict.score == 1.0f)
                    {
                        scoreOneCount++;
                        scoreOnePoints.Add(matchingPredict);
                    }
                }

                // 履歴データが2つの場合、上位2つを選択
                if (scoreOneCount == 2)
                {
                    hasGroupWithTwoScoreOne = true;
                    var topTwo = scoreOnePoints
                    .OrderByDescending(p => p.score)
                    .Take(2)
                    .ToList();
                    selectedResults.AddRange(topTwo);
                }
                else if (scoreOneCount == 0)
                {
                    // 履歴データがない場合、スコアが最も高いものを選択
                    var highestScorePoint = cluster.Value
                    .Select(point => m_PredictResultPointEntityList.FirstOrDefault(p => p.x == point[0] && p.y == point[1]))
                    .Where(p => p != null)
                    .OrderByDescending(p => p.score)
                    .FirstOrDefault();

                    if (highestScorePoint != null)
                    {
                        selectedResults.Add(highestScorePoint);
                    }
                }
            }

            // Score1が2つあるグループがない場合はreturn
            if (!hasGroupWithTwoScoreOne)
            {
                Log.d("No groups with two HistoryData found. Returning.");
                return;
            }

            //選ばれた結果をm_RevisedResultsに追加
            foreach (var result in selectedResults)
            {
                m_RevisedResults.Add(new double[] { result.x, result.y });
            }
            historyIsFixed = true;
        }

        private void ProcessClusteringByKmeansWithDBScan(int dartCount)
        {
            if (dartCount != 3) return;
            if (historyIsFixed) return;

            ProcessByDBScanForCompare(m_PredictHistoryResults, dartCount);
            Dictionary<int, double> clusterAreas = new();

            //それぞれのグループの面積を計算して、大きい順にソートする
            foreach (var cluster in m_DBScan_Results_For_Compare)
            {
                var points = cluster.Value;
                if (points.Count == 0) continue;

                double minX = points.Min(p => p[0]);
                double maxX = points.Max(p => p[0]);
                double minY = points.Min(p => p[1]);
                double maxY = points.Max(p => p[1]);

                double width = maxX - minX;
                double height = maxY - minY;

                var groupArea = width * height;

                var ConvexArea = ConvexHull.ComputeConvexHull(cluster.Value);

                clusterAreas[cluster.Key] = ConvexArea;

                var ConcaveArea = ConcaveHull.ComputeConcaveHull(cluster.Value, 3);
                Log.d($"Cluster {cluster.Value.Count} Concave Hull Area: {ConcaveArea}");
                Log.d($"Cluster {cluster.Value.Count} Convex Hull Area: {ConvexArea}");
                Log.d($"Cluster {cluster.Value.Count}: Width={width}, Height={height} , Area={groupArea}");
            }

            //面積の大きい順にソートした、最初のクラスタを取得
            var TargetCluster = m_DBScan_Results_For_Compare
                                        .OrderByDescending(kv => clusterAreas.ContainsKey(kv.Key) ? clusterAreas[kv.Key] : 0)
                                        .FirstOrDefault();

            if (TargetCluster.Value is null || TargetCluster.Value.Count == 0) return;

            //ターゲットのグループの結果をConf順にソート
            TargetCluster.Value.Sort((x, y) => GetScoreByPositionNoError(y).CompareTo(GetScoreByPositionNoError(x)));

            //Conf１の履歴データが入る
            var targetPoint = TargetCluster.Value[0];

            //ポイントを増やしたグループと同じ履歴データが入っているグループを取得
            var matchingCluster = m_DBScan_Results.FirstOrDefault(kv => kv.Value.Any(p => p.SequenceEqual(targetPoint)));

            //一致する履歴データが入っていないグループ
            var nonMatchingClusters = m_DBScan_Results.Where(kv => !kv.Value.Any(p => p.SequenceEqual(targetPoint))).ToList();

            //元のDBScanグループで選ばれた方を再度Kmeansで２つに分ける
            var FinalKmeansGroup = FinalProcessByKmeans(matchingCluster.Value);
            FinalKmeansGroup.Add(nonMatchingClusters[0].Value);

            //各グループのConfが高い結果を抜き出す
            if (FinalKmeansGroup != null)
            {
                foreach (var item in FinalKmeansGroup)
                {
                    var t = item;
                    try
                    {
                        t.Sort((x, y) => GetScoreByPosition(y).CompareTo(GetScoreByPosition(x)));
                        if (t.Count > 0)
                        {
                            m_RevisedResults.Add(t[0]);
                        }
                        else
                        {
                            Log.e("FinalKmeansGroup sorting resulted in an empty list, unable to add elements.");
                        }
                    }
                    catch (Exception e)
                    {
                        Log.e($"Error occurred while sorting or accessing FinalKmeansGroup: {e.Message}");
                    }
                }
            }
            else
            {
                Log.e("FinalKmeansGroup is null, unable to process.");
            }
            historyIsFixed = true;
        }

        // 辅助方法：处理最终结果
        private async Task<(bool isFixed, List<CompareFilter.PredictWithBox> predictList)> ProcessFinalResults(int throwCount)
        {
            //以下情况是iou算出来的数据和dartCount不匹配的计算
            //如果iou的结果数量多于dartCount，就去获取dbscan的结果
            m_RevisedResults.Clear();

            var t_Fixed = false;
            historyIsFixed = false;
            var finallyResults = new List<CompareFilter.PredictWithBox>();
            if (m_IOU_Results.Count != throwCount)
            {
                t_Fixed = true;
                // 原中文日志：iou算出来的数据一共有{m_IOU_Results.Count}个和throwCount:{throwCount}不匹配
                Log.d($"IoU filtering resulted in {m_IOU_Results.Count} data points, which doesn't match throwCount: {throwCount}");

                // 并行执行左右相机的DartCountPredict
                var dartCountTasks = await Task.WhenAll(
                    DartCountPredict(CameraPos.Left),
                    DartCountPredict(CameraPos.Right)
                );

                // 使用GetMaxDartsCount获取最终结果
                var result = GetMaxDartsCount(dartCountTasks);
                m_PredictResultDartCountList = result.Count > 0 ? result[0].Tag : 0;
                // 原中文日志：镖数为: {m_PredictResultDartCountList}
                Log.d($"Dart count: {m_PredictResultDartCountList}");

                //カウントモデルの数がエコーのカウントより多い場合に、エコーのカウントを使う
                var FinalCountResult = m_PredictResultDartCountList > throwCount ? throwCount : m_PredictResultDartCountList;

                // 执行聚类分析
                ProcessClustering(m_PredictResultPointEntityList, FinalCountResult);

                //如果dbscan的结果数量等于dartCount，那么我们就过滤一下每一个群,保留执行度最高的点
                if (m_DBScan_Results.Count == FinalCountResult)
                {
                    foreach (var item in m_DBScan_Results)
                    {
                        var t = item.Value;
                        try
                        {
                            t.Sort((x, y) => GetScoreByPosition(y).CompareTo(GetScoreByPosition(x)));
                            if (t.Count > 0)
                            {
                                m_RevisedResults.Add(t[0]);
                            }
                            else
                            {
                                // 原中文日志：m_ClusterResults排序后列表为空，无法添加元素
                                Log.e("m_ClusterResults list is empty after sorting, cannot add elements");
                            }
                        }
                        catch (Exception e)
                        {
                            // 原中文日志：m_ClusterResults排序或获取时发生错误
                            Log.e($"Error during sorting or accessing m_ClusterResults: {e.Message}");
                        }
                    }
                }
                else
                {
                    {
                        if (m_DBScan_Results.Count == 2)
                        {
                            CheckHistoryTwoCount();
                            ProcessClusteringByKmeansWithDBScan(throwCount);
                        }
                        if (!historyIsFixed)
                        {
                            //用k-means的结果来过滤
                            foreach (var item in m_KMeans_Results)
                            {
                                var t = item.Value;
                                try
                                {
                                    t.Sort((x, y) => GetScoreByPosition(y).CompareTo(GetScoreByPosition(x)));
                                    if (t.Count > 0)
                                    {
                                        m_RevisedResults.Add(t[0]);
                                    }
                                    else
                                    {
                                        // 原中文日志：m_KMeansResults排序后列表为空，无法添加元素
                                        Log.e("m_KMeansResults list is empty after sorting, cannot add elements");
                                    }
                                }
                                catch (Exception e)
                                {
                                    // 原中文日志：m_KMeansResults排序或获取时发生错误
                                    Log.e($"Error during sorting or accessing m_KMeansResults: {e.Message}");
                                }
                            }
                        }
                    }
                }

                // 处理历史结果和新预测结果的区域比较，并保留不重复的结果
                var predictionZones = new List<(string Key, CompareFilter.PredictWithBox Value)>();

                foreach (var point in m_RevisedResults)
                {
                    filteredPoint.x = (float)point[0];
                    filteredPoint.y = (float)point[1];
                    var zone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                    var score = GetScoreByPosition(point);
                    predictionZones.Add((zone,
                        new CompareFilter.PredictWithBox(filteredPoint.x, filteredPoint.y, score, m_BoxSize)));
                    // 原中文日志：更正之后的区域: {zone}, 坐标为:x:{(float)point[0]},y:{(float)point[1]}, 置信度:{score}
                    Log.d($"Corrected zone: {zone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, confidence: {score}");
                }

                // m_TempResults = throwCount >= 2 ? m_HistoryResults : m_IOU_Results;
                //
                // // 过滤掉与历史结果重复的预测结果
                // foreach (var historyResult in m_TempResults)
                // {
                //     filteredPoint.x = historyResult.x;
                //     filteredPoint.y = historyResult.y;
                //     // 获取区域
                //     var zone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                //     // 原中文日志：推理出来或记录中的区域:{zone}, 坐标为:x:{historyResult.x},y:{historyResult.y}
                //     Log.d($"Inferred or recorded zone: {zone}, coordinates: x={historyResult.x}, y={historyResult.y}");
                // }

                // 原中文日志：开始移除重复的预测结果
                Log.d("Starting to remove duplicate prediction results");
                // 移除重复的预测结果
                var uniquePredictions = new List<(string Key, CompareFilter.PredictWithBox Value)>();
                Log.d($"predictionZones.Count：{predictionZones.Count}");
                // 检查predictionZones中键的数量
                if (predictionZones.Count > 1)
                {
                    // 提取score不等于1的项
                    var t_FinalPredictionZones = predictionZones
                        .Where(prediction => prediction.Value.score != 1.0f)
                        .ToList();
                    uniquePredictions.AddRange(t_FinalPredictionZones);

                    // 原中文日志：提取score不等于1的结果，共有{uniquePredictions.Count}个
                    Log.d($"Extracted results with score not equal to 1, total: {uniquePredictions.Count}");
                }
                else
                {
                    uniquePredictions.AddRange(predictionZones);
                }

                // 原中文日志：更正预测原结果数量: {m_RevisedResults.Count}, 过滤后剩余数量: {uniquePredictions.Count}
                Log.d($"Original corrected prediction results: {m_RevisedResults.Count}, remaining after filtering: {uniquePredictions.Count}");

                // 更新历史结果
                if (uniquePredictions.Count == 0)
                {
                    // 原中文日志：没有可排除区域，更新历史记录
                    Log.d("No excludable zones, updating history record");
                    foreach (var box in m_HistoryResults)
                    {
                        box.score = 1.0f;  // 只需要修改 score 为 1.0f
                    }
                }
                else
                {
                    foreach (var (_, box) in uniquePredictions)
                    {
                        filteredPoint.x = box.x;
                        filteredPoint.y = box.y;
                        var dartZone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                        // 原中文日志：剩余后的区域: {dartZone}，坐标为x：{filteredPoint.x},y:{filteredPoint.y},添加到历史记录中, 置信度为:{box.score}
                        Log.d($"Remaining zone: {dartZone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, added to history record, confidence: {box.score}");
                        box.dartZone = dartZone;
                        m_HistoryResults.Add(box);
                        finallyResults.Add(box);
                        RecoveryService.Save(box);
                    }
                }
            }
            else
            {
                // 先检查每个结果的区域和置信度
                var checkedResults = new List<(CompareFilter.PredictWithBox box, string zone)>();
                foreach (var box in m_IOU_Results)
                {
                    filteredPoint.x = box.x;
                    filteredPoint.y = box.y;
                    var dartZone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                    box.dartZone = dartZone;
                    checkedResults.Add((box, dartZone));
                }

                // 对结果进行排序和筛选
                finallyResults = checkedResults
                    .AsParallel()
                    .Select(r =>
                    {
                        if (r.zone.StartsWith("Miss"))
                        {
                            r.box.score *= 0.5f; // 降低Miss的置信度
                        }

                        return r.box; // 返回盒子对象
                    })
                    .Take(throwCount) // 选择前 _ThrowCount + 1 个结果
                    .ToList(); // 转换为列表

                m_HistoryResults.Clear(); // 清空历史结果
                RecoveryService.ClearAll();
                // 处理最终结果
                foreach (var box in finallyResults)
                {
                    filteredPoint.x = box.x;
                    filteredPoint.y = box.y;
                    var dartZone = m_CalibrationTools[0].GetDartZone(filteredPoint, true);
                    // 原中文日志：定位出打镖数据为:{dartZone}, 坐标为x：{filteredPoint.x},y:{filteredPoint.y},置信度为:{box.score}
                    Log.d($"Located dart data: {dartZone}, coordinates: x={filteredPoint.x}, y={filteredPoint.y}, confidence: {box.score}");
                    box.dartZone = dartZone;
                    m_HistoryResults.Add(box);
                    RecoveryService.Save(box);
                }

                // 原中文日志：经过IoU算法过滤后, 数据量一共有{finallyResults.Count}个
                Log.d($"After IoU algorithm filtering, there are a total of {finallyResults.Count} data points");
            }

            this.IsBusy = false;

            m_DetectionTakesEndTime = DateTime.Now;
            m_DetectionTakesTime = (m_DetectionTakesEndTime - m_DetectionTakesStartTime).TotalMilliseconds;

            return (t_Fixed, m_HistoryResults);
        }

        /// <summary>
        /// 立刻拍照并直接推理结果
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="runNMS"></param>
        /// <returns></returns>
        private async Task<(bool isSuccess, List<TFEntities.PredictResultPointEntity> predictResultList)> Predict(CameraPos pos, bool runNMS = true, CancellationToken cancellationToken = default)
        {
            var t_CameraRespond = await GetImage(pos);
            if (t_CameraRespond.IsSuccess)
            {
                return (t_CameraRespond.IsSuccess,
                    await DartPredict(pos, t_CameraRespond.Texture, runNMS, cancellationToken));
            }
            else
            {
                return (t_CameraRespond.IsSuccess, new List<TFEntities.PredictResultPointEntity>());
            }
        }

        /// <summary>
        /// 使用临时存储的图片进行推理
        /// </summary>
        /// <param name="pos">相机位置</param>
        /// <param name="texture">临时存储的图片</param>
        /// <param name="runNMS">是否运行NMS</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>返回推理结果</returns>
        private async Task<(bool isSuccess, List<TFEntities.PredictResultPointEntity> predictResultList)> PredictWithStoredImage(CameraPos pos, Texture texture, bool runNMS = true, CancellationToken cancellationToken = default)
        {
            return texture == null ? (false, new List<TFEntities.PredictResultPointEntity>()) : (true, await DartPredict(pos, texture, runNMS, cancellationToken));
        }

        /// <summary>
        /// 让指定的相机对指定图片进行推理
        /// </summary>
        /// <param name="pos"></param>
        /// <param name="texture"></param>
        /// <param name="runNMS"></param>
        /// <returns></returns>
        private async Task<List<TFEntities.PredictResultPointEntity>> DartPredict(CameraPos pos, Texture texture, bool runNMS = true, CancellationToken cancellationToken = default)
        {
            var positions = new List<TFEntities.PredictResultPointEntity>();
            if (texture == null)
            {
                Log.d("Texture is null");
            }

            try
            {
                positions = await GetDartPredictor(pos).Predict(texture, cancellationToken, runNMS)
                    .TimeoutAfter(TimeSpan.FromSeconds(3));
            }
            catch (TimeoutException)
            {
                Log.e("Predict TimeOut");
            }
            catch (Exception e)
            {
                Log.e(e.Message);
            }

            return positions;
        }

        public async Task<int> DartPredictForAutoChange(CameraPos pos, CancellationToken cancellationToken = default)
        {
            var isUSBMode = Get_USBMode();

            if (isUSBMode)
            {
                // USB模式下，使用优化的GetUSBCameraImage方法获取图像
                int cameraIndex = (int)pos + 1; // 将CameraPos转换为USB相机索引

                try
                {
                    // 使用优化后的图像获取方法
                    var texture = await GetUSBCameraImage(cameraIndex);

                    if (texture != null)
                    {
                        // 如果需要，可以添加延时确保图像完全准备好
                        await Task.Delay(50);

                        // 将Texture转换为Texture2D（如果需要）
                        Texture2D texture2D = texture as Texture2D;
                        if (texture2D == null && texture is RenderTexture renderTexture)
                        {
                            // 如果是RenderTexture，转换为Texture2D
                            texture2D = new Texture2D(renderTexture.width, renderTexture.height, TextureFormat.RGBA32, false);
                            RenderTexture.active = renderTexture;
                            texture2D.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
                            texture2D.Apply();
                            RenderTexture.active = null;
                        }

                        if (texture2D != null)
                        {
                            var DartPredictResult = await DartPredict(pos, texture2D, true, cancellationToken);
                            Log.d($"DartPredictCount {DartPredictResult.Count}");
                            return DartPredictResult.Count;
                        }
                        else
                        {
                            Log.e($"无法将图像转换为Texture2D - 相机位置: {pos}");
                        }
                    }
                    else
                    {
                        Log.e($"获取USB相机图像失败 - 相机位置: {pos}");
                    }
                }
                catch (Exception ex)
                {
                    Log.e($"处理USB相机图像时出错: {ex.Message}");
                }

                return 0;
            }
            else
            {
                // 非USB模式，使用原有方法获取图像
                var t_CameraRespond = await GetImage(pos);
                if (t_CameraRespond.IsSuccess)
                {
                    var DartPredictResult = await DartPredict(pos, t_CameraRespond.Texture, true, cancellationToken);
                    Log.d($"DartPredictCount {DartPredictResult.Count}");
                    return DartPredictResult.Count;
                }
                else
                {
                    return 0;
                }
            }
        }

        public async Task<List<TFEntities.PredictResultPointEntity>> DartCountPredict(CameraPos pos, CancellationToken cancellationToken = default)
        {
            var isUSBMode = Get_USBMode();

            if (isUSBMode)
            {
                // USB模式下，使用优化的GetUSBCameraImage方法获取图像
                int cameraIndex = (int)pos + 1; // 将CameraPos转换为USB相机索引

                try
                {
                    // 使用优化后的图像获取方法
                    var texture = await GetUSBCameraImage(cameraIndex);

                    if (texture != null)
                    {
                        // 如果需要，可以添加延时确保图像完全准备好
                        await Task.Delay(50);

                        // 将Texture转换为Texture2D（如果需要）
                        Texture2D texture2D = texture as Texture2D;
                        if (texture2D == null && texture is RenderTexture renderTexture)
                        {
                            // 如果是RenderTexture，转换为Texture2D
                            texture2D = new Texture2D(renderTexture.width, renderTexture.height, TextureFormat.RGBA32, false);
                            RenderTexture.active = renderTexture;
                            texture2D.ReadPixels(new Rect(0, 0, renderTexture.width, renderTexture.height), 0, 0);
                            texture2D.Apply();
                            RenderTexture.active = null;
                        }

                        if (texture2D != null)
                        {
                            Debug.Log($"成功获取USB相机图像用于飞镖计数 - 相机位置: {pos}, 尺寸: {texture2D.width}x{texture2D.height}");
                            return await DartCountPredict(pos, texture2D, cancellationToken);
                        }
                        else
                        {
                            Debug.LogError($"无法将图像转换为Texture2D - 相机位置: {pos}");
                        }
                    }
                    else
                    {
                        Debug.LogError($"获取USB相机图像失败 - 相机位置: {pos}");
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogError($"处理USB相机图像时出错: {ex.Message}");
                }

                return new List<TFEntities.PredictResultPointEntity>();
            }
            else
            {
                // 非USB模式，使用原有方法获取图像
                var t_CameraRespond = await GetImage(pos);
                if (t_CameraRespond.IsSuccess)
                {
                    return await DartCountPredict(pos, t_CameraRespond.Texture, cancellationToken);
                }
                else
                {
                    return new List<TFEntities.PredictResultPointEntity>();
                }
            }
        }

        private async Task<List<TFEntities.PredictResultPointEntity>> DartCountPredict(CameraPos pos, Texture texture, CancellationToken cancellationToken = default)
        {
            var t_Result = new List<TFEntities.PredictResultPointEntity>();
            try
            {
                t_Result = await GetDartCountPredictor(pos).Predict(texture, cancellationToken)
                    .TimeoutAfter(TimeSpan.FromSeconds(3));
            }
            catch (TimeoutException)
            {
                Log.e("Predict TimeOut");
                return t_Result;
            }
            catch (Exception e)
            {
                Log.e(e.Message);
            }
            return t_Result;
        }

        public async Task<List<TFEntities.PredictResultPointEntity>> PredictDartWithoutDartsCount(CameraPos pos, Texture texture, bool runNMS = true)
        {
            var t_Result = new List<TFEntities.PredictResultPointEntity>();
            try
            {
                t_Result = await GetDartPredictor(pos).Predict(texture, CancellationToken.None, runNMS)
                    .TimeoutAfter(TimeSpan.FromSeconds(3));
            }
            catch (TimeoutException)
            {
                Log.e("Predict TimeOut");
                return t_Result;
            }
            catch (Exception e)
            {
                Log.e(e.Message);
            }

            return t_Result;
        }

        public async Task<List<TFEntities.PredictResultPointEntity>> PredictDartZoom(Texture zoomTexture, CancellationToken cancellationToken = default)
        {
            var t_Result = new List<TFEntities.PredictResultPointEntity>();
            try
            {
                t_Result = await GetZoomPredictor().ZoomPredict(zoomTexture, cancellationToken);
            }
            catch (Exception e)
            {
                Log.e(e.Message);
            }
            return t_Result;
        }

        public void ProcessClustering(List<CompareFilter.PredictWithBox> points, int throwCount)
        {
            this.ProcessByDBScan(points, throwCount);
            this.ProcessByKmeans(points, throwCount);
        }

        /// <summary>
        /// 初始化镖尖缩放预测器
        /// </summary>
        public async Task InitializeDartZoomPredictor()
        {
            if (m_DartZoomPredictors != null && m_DartZoomPredictors.gameObject != null)
            {
                Log.d("DartZoom detector already initialized.");
                return;
            }

            await InitDartZoomPredictor();
            Log.d("DartZoom detector initialized successfully.");
        }

        /// <summary>
        /// 终止镖尖缩放预测器
        /// </summary>
        public void TerminateDartZoomPredictor()
        {
            if (m_DartZoomPredictors == null || m_DartZoomPredictors.gameObject == null)
            {
                Log.d("DartZoom detector is not initialized.");
                return;
            }

            // 先调用Dispose方法释放TensorFlow资源
            try
            {
                // 尝试调用IDeepLearnDetector接口的Dispose方法来释放TensorFlow资源
                var detector = m_DartZoomPredictors.GetComponent<IDeepLearnDetector>();
                if (detector != null)
                {
                    detector.Dispose();
                    Log.d("DartZoom detector resources disposed successfully.");
                }
            }
            catch (Exception ex)
            {
                Log.e($"Error disposing DartZoom detector resources: {ex.Message}");
            }

            // 然后销毁DartZoomPredictor游戏对象
            if (m_DartZoomPredictors.gameObject != null)
            {
                GameObject.Destroy(m_DartZoomPredictors.gameObject);

                // 检查容器是否为空，如果为空则销毁容器
                GameObject containerObj = GameObject.Find("GranEyeSDK_DontDestroy");
                if (containerObj != null && containerObj.transform.childCount == 0)
                {
                    GameObject.Destroy(containerObj);
                }
            }

            // 清空引用
            m_DartZoomPredictors = null;

            Log.d("DartZoom detector terminated successfully.");
        }

        private async Task InitDartZoomPredictor()
        {
            // 先查找已存在的DontDestroy容器
            var containerObj = GameObject.Find("GranEyeSDK_DontDestroy");

            if (containerObj == null)
            {
                // 创建容器并设置为DontDestroyOnLoad
                containerObj = new GameObject("GranEyeSDK_DontDestroy");
                UnityEngine.Object.DontDestroyOnLoad(containerObj);
            }

            // 查找已有的检测器（在DontDestroyOnLoad容器下查找）
            var existingDetector = containerObj.GetComponentInChildren<DartZoomPredictor>(true);

            if (existingDetector != null)
            {
                // 使用已存在的检测器
                m_DartZoomPredictors = existingDetector;
            }
            else
            {
                // 销毁场景中可能存在的其他检测器实例
                var otherDetectors = UnityEngine.Object.FindObjectsByType<DartZoomPredictor>(FindObjectsSortMode.None);
                foreach (var detector in otherDetectors)
                {
                    if (detector.transform.parent == null || detector.transform.parent.name != "GranEyeSDK_DontDestroy")
                    {
                        GameObject.Destroy(detector.gameObject);
                    }
                }

                // 创建新的检测器作为容器的子对象
                var detectorObj = new GameObject("DartZoomPredictor");
                detectorObj.transform.SetParent(containerObj.transform);

                m_DartZoomPredictors = detectorObj.AddComponent<DartZoomPredictor>();
            }

            // 初始化检测器
            await m_DartZoomPredictors.Init("DartZoom.tflite", TfLiteDelegateType.XNNPACK);
        }
        #endregion
    }
}
