using System.Threading.Tasks;
using com.luxza.graneye;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

namespace com.luxza.graneye.examples
{
    /// <summary>
    /// GranEye统一AutomaticCalibration方法使用示例
    /// 展示如何使用新的整合后的自动校准功能
    /// </summary>
    public class AutoCalibrationUsageExample : MonoBehaviour
    {
        [Header("UI组件")]
        public RawImage boardImage;
        public RectTransform dotPrefab;
        public Button calibrateButton;
        public Button calibrateWithMLButton;
        public Button calibrateWithDebugButton;
        
        [Header("设置")]
        public GranEyeCore.CameraPos cameraPosition = GranEyeCore.CameraPos.Left;
        
        private void Start()
        {
            // 绑定按钮事件
            calibrateButton.onClick.AddListener(() => _ = BasicCalibrationExample());
            calibrateWithMLButton.onClick.AddListener(() => _ = MLCalibrationExample());
            calibrateWithDebugButton.onClick.AddListener(() => _ = DebugCalibrationExample());
        }

        /// <summary>
        /// 基础校准示例 - 最简单的使用方式
        /// </summary>
        private async UniTask BasicCalibrationExample()
        {
            try
            {
                // 获取相机图像
                var cameraData = await GranEye.Instance.GetMainCameraViewData();
                var texture = cameraData.LeftCameraTexture as Texture2D;
                
                if (texture == null)
                {
                    Debug.LogError("无法获取相机纹理");
                    return;
                }
                
                // 显示图像
                boardImage.texture = texture;
                
                // 执行基础自动校准
                bool success = await GranEye.Instance.AutomaticCalibration(cameraPosition, texture);
                
                if (success)
                {
                    Debug.Log("基础自动校准成功！");
                }
                else
                {
                    Debug.LogWarning("基础自动校准失败");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"基础校准过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 带ML预测的校准示例 - 可以预测缺失的点
        /// </summary>
        private async UniTask MLCalibrationExample()
        {
            try
            {
                // 获取相机图像
                var cameraData = await GranEye.Instance.GetMainCameraViewData();
                var texture = cameraData.LeftCameraTexture as Texture2D;
                
                if (texture == null)
                {
                    Debug.LogError("无法获取相机纹理");
                    return;
                }
                
                // 显示图像
                boardImage.texture = texture;
                
                // 执行带ML预测的自动校准
                bool success = await GranEye.Instance.AutomaticCalibrationWithML(cameraPosition, texture, true);
                
                if (success)
                {
                    Debug.Log("ML增强自动校准成功！");
                }
                else
                {
                    Debug.LogWarning("ML增强自动校准失败");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"ML校准过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 调试模式校准示例 - 显示检测点和详细信息
        /// </summary>
        private async UniTask DebugCalibrationExample()
        {
            try
            {
                // 获取相机图像
                var cameraData = await GranEye.Instance.GetMainCameraViewData();
                var texture = cameraData.LeftCameraTexture as Texture2D;
                
                if (texture == null)
                {
                    Debug.LogError("无法获取相机纹理");
                    return;
                }
                
                // 显示图像
                boardImage.texture = texture;
                
                // 清除之前的检测点
                ClearPreviousDots();
                
                // 执行调试模式的自动校准
                var result = await GranEye.Instance.AutomaticCalibrationWithDebug(
                    cameraPosition, 
                    texture,
                    boardImage.rectTransform,
                    dotPrefab,
                    true
                );
                
                // 显示详细结果
                Debug.Log($"校准结果: {(result.IsSuccess ? "成功" : "失败")}");
                Debug.Log($"检测到的点数: {result.DetectedPointsCount}");
                Debug.Log($"调试信息: {result.DebugInfo}");
                
                if (!result.IsSuccess)
                {
                    Debug.LogWarning($"校准失败原因: {result.ErrorMessage}");
                }
                
                // 显示检测到的点信息
                foreach (var point in result.DetectedPoints)
                {
                    Debug.Log($"点 {point.Key}: 位置 ({point.Value.x:F2}, {point.Value.y:F2})");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"调试校准过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 高级自定义校准示例 - 完全自定义配置
        /// </summary>
        private async UniTask AdvancedCalibrationExample()
        {
            try
            {
                // 获取相机图像
                var cameraData = await GranEye.Instance.GetMainCameraViewData();
                var texture = cameraData.LeftCameraTexture as Texture2D;
                
                if (texture == null)
                {
                    Debug.LogError("无法获取相机纹理");
                    return;
                }
                
                // 创建自定义配置
                var options = new GranEye.AutoCalibrationOptions
                {
                    EnableMLPrediction = true,
                    ShowDebugInfo = true,
                    IsTestMode = false,  // 不显示检测点
                    TargetTexture = texture
                };
                
                // 执行自定义配置的校准
                var result = await GranEye.Instance.AutomaticCalibration(cameraPosition, texture, options);
                
                // 处理结果
                if (result.IsSuccess)
                {
                    Debug.Log("高级自定义校准成功！");
                    Debug.Log($"检测信息: {result.DebugInfo}");
                }
                else
                {
                    Debug.LogWarning($"高级自定义校准失败: {result.ErrorMessage}");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"高级校准过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 清除之前显示的检测点
        /// </summary>
        private void ClearPreviousDots()
        {
            // 清除boardImage下的所有子对象（检测点）
            for (int i = boardImage.transform.childCount - 1; i >= 0; i--)
            {
                DestroyImmediate(boardImage.transform.GetChild(i).gameObject);
            }
        }

        /// <summary>
        /// 批量校准示例 - 对多个相机位置进行校准
        /// </summary>
        private async UniTask BatchCalibrationExample()
        {
            var cameraPositions = new[] { GranEyeCore.CameraPos.Left, GranEyeCore.CameraPos.Right };
            
            foreach (var pos in cameraPositions)
            {
                try
                {
                    Debug.Log($"开始校准相机: {pos}");
                    
                    // 这里应该获取对应相机的图像
                    // 为了示例，我们使用主相机的图像
                    var cameraData = await GranEye.Instance.GetMainCameraViewData();
                    var texture = cameraData.LeftCameraTexture as Texture2D;
                    
                    if (texture != null)
                    {
                        bool success = await GranEye.Instance.AutomaticCalibrationWithML(pos, texture);
                        Debug.Log($"相机 {pos} 校准结果: {(success ? "成功" : "失败")}");
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"相机 {pos} 校准失败: {ex.Message}");
                }
            }
        }
    }
}
